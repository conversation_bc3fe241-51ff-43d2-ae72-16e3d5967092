import { Injectable, Inject, HttpException, HttpStatus } from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { S3Service } from '../common/s3.service';
import { v4 as uuidv4 } from 'uuid';
import { ExternalService } from '../common/external.service';
import { SNS } from 'aws-sdk';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { LimPriceBookSelector } from '../pricebook/lim.seclector';
import { IbatElPriceBookSelector } from '../pricebook/ibat_el.seclector';
import { HzuPriceBookSelector } from '../pricebook/hzu.selector';
import { UtilityService } from '../common/utility.service';
import axios, { AxiosRequestConfig } from 'axios';
import { UcwAgentPriceBookSelector } from '../pricebook/ucw_agent.selector';
import { UnfcAgentPriceBookSelector } from '../pricebook/unfc_agent.selector';
import { LookUpService } from '../lookup/service';
import { SalesforceService } from '../common/salesforce.service';
import { LsbfmyrPriceBookSelector } from '../pricebook/lsbfmyr.selector';
import { OapTranslationService } from '../oapTranslation/service';
import { DefaultPriceBookSelector } from '../pricebook/default.selector';
import {
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { FileParserService, ParsedUserData } from '../common/file-parser.service';
import {
  UserMigrationConfigDto,
  UserMigrationResultDto,
  UserMigrationResponseDto,
  ApplicationMigrationConfigDto,
  ApplicationMigrationResponseDto,
  ApplicationMigrationResultDto,
  DocumentStorageResultDto,
  PicklistMappingResultDto,
} from './dto/legacy-user.dto';
import { getBrandMappings, ApplicationMappingConfig } from './mappings';
import { transformCountryFields } from './utils/country-mapping';
import { transformPhoneNumberFields } from './utils/phone-formatting';
@Injectable()
export class OapService {
  private sns: AWS.SNS;
  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly dynamoDBService: DynamoDBService,
    private readonly s3Service: S3Service,
    private readonly externalService: ExternalService,
    private readonly limPriceBookSelector: LimPriceBookSelector,
    private readonly ibatElPriceBookSelector: IbatElPriceBookSelector,
    private readonly hzuPriceBookSelector: HzuPriceBookSelector,
    private readonly ucwAgentPriceBoolSelector: UcwAgentPriceBookSelector,
    private readonly unfcAgentPriceBookSelector: UnfcAgentPriceBookSelector,
    private readonly lsbfmyrPriceBookSelector: LsbfmyrPriceBookSelector,
    private readonly utilityService: UtilityService,
    private readonly loggerEnum: LoggerEnum,
    private readonly lookUpService: LookUpService,
    private readonly salesforceService: SalesforceService,
    private readonly oapTranslationService: OapTranslationService,
    private readonly defaultPriceBookSelector: DefaultPriceBookSelector,
    private readonly fileParserService: FileParserService,
  ) {
    this.sns = new SNS({ region: process.env.REGION });
  }

  private getUtmWebsite(brand: string): string {
    switch (brand) {
      case 'UCW':
        return process.env.UTM_WEBSITE_UCW;
      case 'UEG':
        return process.env.UTM_WEBSITE_UEG;
      default:
        return;
    }
  }

  async verifyRecaptcha(token: string, action: string): Promise<any> {
    try {
      const secretKey = process.env.RECAPTCHA_SECRET_KEY;

      if (!secretKey) {
        throw new HttpException(
          'reCAPTCHA secret key not configured',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const verificationUrl = 'https://www.google.com/recaptcha/api/siteverify';
      const params = new URLSearchParams();
      params.append('secret', secretKey);
      params.append('response', token);

      const response = await axios.post(verificationUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const verificationResult = response.data;

      if (!verificationResult.success) {
        return {
          success: false,
          error: 'reCAPTCHA verification failed',
          details: verificationResult['error-codes'] || [],
        };
      }

      // For reCAPTCHA v3, check the score and action
      if (verificationResult.score !== undefined) {
        if (verificationResult.action !== action) {
          return {
            success: false,
            error: 'reCAPTCHA action mismatch',
            details: [
              `Expected action: ${action}, received: ${verificationResult.action}`,
            ],
          };
        }

        return {
          success: true,
          score: verificationResult.score,
          action: verificationResult.action,
        };
      }

      // For reCAPTCHA v2, just return success
      return {
        success: true,
        action: action,
      };
    } catch (error) {
      // Log the error with proper CloudWatch logger signature
      const currentUTC = new Date().toISOString();
      await this.cloudWatchLoggerService.error(
        'recaptcha-verification',
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Event.UPLOAD_DOCUMENT,
        this.loggerEnum.UseCase.UPLOAD_DOCUMENT,
        { token: token.substring(0, 10) + '...', action },
        null,
        error.message || 'reCAPTCHA verification failed',
        'OAP',
        '',
        `oap-backend/recaptcha/${currentUTC}`,
        'reCAPTCHA_Token',
        token.substring(0, 10) + '...',
        'reCAPTCHA_Verification',
        action,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'reCAPTCHA verification service unavailable',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async getStudentDetails(email: string, oapName: string): Promise<any> {
    const studentDetails = await this.dynamoDBService.getObject(
      process.env.GUS_OAP_STUDENT_DETAILS_TABLE,
      {
        PK: email,
        SK: oapName,
      },
    );
    return studentDetails;
  }

  async getOapDetails(
    oapName: string,
    mode: string,
    language = null,
  ): Promise<any> {
    try {
      let oapDetails = await this.dynamoDBService.getObject(
        process.env.GUS_OAP_TABLE,
        {
          PK: oapName,
          SK: mode,
        },
      );
      if (language) {
        oapDetails.Item = await this.oapTranslationService.translateJson(
          oapDetails.Item,
          language,
        );
      }
      return await this.updateS3SignedUrl(oapDetails);
    } catch (error) {
      throw error;
    }
  }
  async getSections(
    oapName: string,
    form: string,
    mode,
    language = null,
  ): Promise<any> {
    try {
      const params = {
        TableName: process.env.GUS_OAP_FORM_SECTION_TABLE,
        KeyConditionExpression: 'PK = :pkValue and begins_with(SK, :skValue)',
        ExpressionAttributeValues: {
          ':pkValue': `${oapName}_${mode}`,
          ':skValue': `${form}`,
        },
      };
      const applicantDetails = await this.dynamoDBService.queryObjects(params);
      if (language) {
        return await Promise.all(
          applicantDetails?.Items.map(async (item) => {
            return await this.oapTranslationService.translateJson(
              item,
              language,
            );
          }),
        );
      }
      return applicantDetails?.Items;
    } catch (error) {
      throw error;
    }
  }
  async getDocumentIdsByApplicationDetails(data): Promise<any> {
    const documentIds = [];

    function findIds(obj) {
      for (const key in obj) {
        const value = obj[key];

        if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item && typeof item === 'object' && 'documentId' in item) {
              documentIds.push(item.documentId);
            } else if (item && typeof item === 'object') {
              for (const innerKey in item) {
                if (Array.isArray(item[innerKey])) {
                  for (const docId of item[innerKey]) {
                    documentIds.push(docId?.documentId);
                  }
                }
              }
            }
          });
        } else if (typeof value === 'object' && value !== null) {
          findIds(value);
        }
      }
    }

    findIds(data);
    return documentIds;
  }
  async getAllDocs(oapName: string, applicationDetails): Promise<any> {
    try {
      const params = {
        TableName: process.env.STUDENT_DOCUMENTS,
        KeyConditionExpression: 'PK = :pkValue and begins_with(SK, :skValue)',
        ExpressionAttributeValues: {
          ':pkValue': applicationDetails.email,
          ':skValue': `${oapName}_${applicationDetails.applicationId}_`,
        },
      };
      const docs = await this.dynamoDBService.queryObjects(params);
      const docIds = await this.getDocumentIdsByApplicationDetails(
        applicationDetails,
      );
      return docs?.Items?.filter(
        (doc) =>
          doc.documentType !== 'Signature' &&
          (docIds.includes(doc.documentId) ||
            doc.documentType === 'Application form' ||
            doc.documentType === 'Accreditation Disclosure Form' ||
            doc.documentType === 'Consumer Information Disclosure Form' ||
            doc.documentType === 'Enrollment Agreement' ||
            doc.documentType ===
            'International Student Statement of Understanding'),
      );
    } catch (error) {
      throw error;
    }
  }
  async updateS3SignedUrl(details: any): Promise<any> {
    try {
      const generateSignedUrl = async (image: any) => {
        if (image.storageLocation === 'S3') {
          const signedUrl = await this.buildS3SignedUrl(
            image.bucketName,
            image.imageKey,
            image.roleArn,
          );
          return signedUrl;
        }
        return image;
      };

      const { Item } = details;

      if (Item?.logoInfo?.storageDetails) {
        const storageDetails = Item.logoInfo.storageDetails;
        delete Item.logoInfo.storageDetails;
        Item.logoInfo.signedUrl = await generateSignedUrl(storageDetails);
      }

      if (Item?.layout?.backgroundImage) {
        const storageDetails = Item.layout.backgroundImage;
        delete Item.layout.backgroundImage;
        Item.layout.backgroundImage = await generateSignedUrl(storageDetails);
      }

      if (Item?.displayNameLogo) {
        const storageDetails = Item.displayNameLogo;
        delete Item.displayNameLogo;
        Item.displayNameLogo = await generateSignedUrl(storageDetails);
      }

      if (Item?.fieldData) {
        const cardFields = Item?.fieldData.filter(
          (item) => item.type === 'cards',
        );
        if (cardFields.length > 0) {
          await Promise.all(
            cardFields.map(async (card) => {
              const cardBg = [];
              if (card.cardBg) {
                const cardBg = await Promise.all(
                  card.cardBg.map(async (img) => {
                    return await generateSignedUrl(img.storageDetails);
                  }),
                );
                delete card.cardBg;
                card.cardBgg = cardBg;
              }
            }),
          );
        }
      }
      if (Item?.languageData) {
        const flagDetails = Item?.languageData?.flagInfo;
        if (flagDetails?.length > 0) {
          await Promise.all(
            flagDetails.map(async (flag) => {
              if (flag.storageDetails) {
                flag.signedUrl = await generateSignedUrl(flag.storageDetails);
                delete flag.storageDetails;
              }
            }),
          );
        }
      }
      return details;
    } catch (error) {
      throw error;
    }
  }
  async getOapFormsDetails(oapName, form, mode, language = null): Promise<any> {
    try {
      const formDetails = await this.dynamoDBService.getObject(
        process.env.GUS_OAP_FORM_TABLE,
        {
          PK: `${oapName}_${mode}`,
          SK: form,
        },
      );
      if (language) {
        formDetails.Item = await this.oapTranslationService.translateJson(
          formDetails.Item,
          language,
        );
      }
      return await this.updateS3SignedUrl(formDetails);
    } catch (error) {
      throw error;
    }
  }
  async getOapFormSectionsDetails(
    oapName: string,
    mode: string,
    formaName: string,
    sectionName: string,
    language = null,
  ): Promise<any> {
    try {
      let sectionDetails = await this.dynamoDBService.getObject(
        process.env.GUS_OAP_FORM_SECTION_TABLE,
        {
          PK: `${oapName}_${mode}`,
          SK: `${formaName}_${sectionName}`,
        },
      );
      if (language) {
        sectionDetails.Item = await this.oapTranslationService.translateJson(
          sectionDetails.Item,
          language,
        );
      }
      return await this.updateS3SignedUrl(sectionDetails);
    } catch (error) {
      throw error;
    }
  }

  async buildS3SignedUrl(bucket, path, roleArn): Promise<any> {
    try {
      const s3 = await this.s3Service.getS3CredentialsByRole(roleArn);
      const params = {
        Bucket: bucket,
        Key: path,
        Expires: 3600,
      };
      const signedUrl = await new Promise((resolve, reject) => {
        s3.getSignedUrl('getObject', params, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });
      return signedUrl;
    } catch (error) {
      throw error;
    }
  }
  async getBase64File(
    bucket: string,
    key: string,
    roleArn: string,
  ): Promise<string> {
    try {
      console.log('Inside get base 64 file');
      const s3 = await this.s3Service.getS3CredentialsByRole(roleArn);
      console.log('s3 -->', s3);
      const params = {
        Bucket: bucket,
        Key: key,
      };
      console.log('params');

      const data = await new Promise<AWS.S3.GetObjectOutput>(
        (resolve, reject) => {
          s3.getObject(params, (err, data) => {
            if (err) {
              console.log('Error in getObject ->', err);
              reject(err);
            } else {
              resolve(data);
            }
          });
        },
      );

      console.log('Data bofy ->', data.Body);
      if (!data.Body) {
        throw new Error('File not found or empty');
      }

      const base64File = data.Body.toString('base64');
      console.log('base64File string ->', base64File);
      return base64File;
    } catch (error) {
      throw error;
    }
  }
  async getStudentDetailsById(
    oapName: string,
    applicationId: string,
    agentContactUserId: string,
  ): Promise<any> {
    try {
      const params = {
        TableName: process.env.STUDENT_DETAILS,
        IndexName: 'applicationId',
        KeyConditionExpression: 'SK = :skValue',
        ExpressionAttributeValues: {
          ':skValue': `${oapName}_${applicationId}`,
        },
      };

      const studentDetails = await this.dynamoDBService.queryObjects(params);
      if (agentContactUserId !== studentDetails.Items[0].agentContactUserId) {
        throw new HttpException(
          'You are forbidden from accessing this application',
          HttpStatus.FORBIDDEN,
        );
      }
      if (!studentDetails.Items || studentDetails.Items.length === 0) {
        throw new HttpException(
          'No details found for the specified oapName and applicationId',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return studentDetails.Items[0];
    } catch (error) {
      throw error;
    }
  }

  async getStudentsOapApplications(
    email: string,
    brand: string,
    language?: string,
  ): Promise<any> {
    const params = {
      TableName: process.env.STUDENT_DETAILS,
      KeyConditionExpression: 'PK = :pkValue',
      FilterExpression:
        'begins_with(brand, :brandValue) AND applicationFilledBy = :applicationFilledBy AND (attribute_not_exists(notMetEligibilityCreteria) OR notMetEligibilityCreteria = :notMetEligibilityCreteria)',
      ExpressionAttributeValues: {
        ':pkValue': email,
        ':brandValue': brand,
        ':applicationFilledBy': 'student',
        ':notMetEligibilityCreteria': false,
      },
    };

    try {
      const studentApplicationsResponse =
        await this.dynamoDBService.queryObjects(params);
      const studentApplications = studentApplicationsResponse.Items;

      const studentsApplicationsDetails = studentApplications.map(
        (application) => ({
          applicationId: application.applicationId,
          isCompleted: application.applicationStatus === 'submitted',
          programName: application.programDisplayName,
          completionPercentage: application.progress,
          sections: application.sections,
          programId: application.program,
          intake: application.startTerm || application.intake,
          ocrReprocessCount: application.ocrReprocessCount,
        }),
      );

      // Translate section names if language parameter is provided
      if (language) {
        for (const application of studentsApplicationsDetails) {
          if (application.sections && application.sections.length > 0) {
            application.sections = await Promise.all(
              application.sections.map(async (section) => {
                // Get translated section name from localization table
                const translationResult = await this.dynamoDBService.getObject(
                  `gus-oap-localization-${process.env.STAGE}`,
                  {
                    PK: language,
                    SK: section.name,
                  },
                );

                // If translation exists, use it; otherwise keep original name
                if (translationResult.Item?.text) {
                  return {
                    ...section,
                    name: translationResult.Item.text,
                  };
                }
                return section;
              }),
            );
          }
        }
      }

      return studentsApplicationsDetails;
    } catch (error) {
      console.error('Error querying student applications:', error);
      throw error;
    }
  }

  async getOapApplicantDetails(
    oapName: string,
    email: string,
    applicationId: string,
  ): Promise<any> {
    try {
      const applicantDetails = await this.dynamoDBService.getObject(
        process.env.STUDENT_DETAILS,
        {
          PK: email,
          SK: `${oapName}_${applicationId}`,
        },
      );

      if (
        applicantDetails.Item.applicationStatus === 'submitted' &&
        applicantDetails.Item.applicationFilledBy === 'student'
      ) {
        const documentFilePathResponse =
          await this.dynamoDBService.queryObjects({
            TableName: process.env.STUDENT_DOCUMENTS,
            KeyConditionExpression:
              'PK = :pkValue and begins_with(SK, :applicationIdValue)',
            FilterExpression: 'contains(documentType, :documentType)',
            ExpressionAttributeValues: {
              ':pkValue': email,
              ':applicationIdValue': `${oapName}_${applicationId}`,
              ':documentType': 'Application form',
            },
          });
        console.log(documentFilePathResponse);
        const documentFilePath = documentFilePathResponse?.Items[0]?.path;
        let bucketName;
        switch (applicantDetails.Item?.brand || oapName) {
          case 'UEG':
            bucketName = process.env.CAMPUSNET_BUCKET_NAME;
            break;
          default:
            bucketName = process.env.REVIEW_CENTER_BUCKET_NAME;
        }
        const signedUrl = await this.buildGetFileSignedUrl(
          bucketName,
          documentFilePath,
          process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
        );

        applicantDetails.Item.signedUrl = signedUrl;
      }
      if ('notMetEligibilityCreteria' in applicantDetails.Item) {
        applicantDetails.Item['metEligibilityCreteria'] =
          !applicantDetails.Item.notMetEligibilityCreteria;
        delete applicantDetails.Item.notMetEligibilityCreteria;
      }
      return applicantDetails?.Item;
    } catch (error) {
      throw error;
    }
  }

  async getPicklistData(
    fieldName: string,
    brand?: string,
    displayLanguage = 'en',
    filterParams?: string,
  ): Promise<any[]> {
    console.log('Inside get picklist data');
    const key = filterParams ? `${fieldName}#${filterParams}` : fieldName;
    const sk = brand ? `${key}#${brand}` : key;
    const pk = displayLanguage;
    let picklistData = await this.dynamoDBService.getObject(
      `gus-eip-picklist-${process.env.STAGE}`,
      {
        PK: pk,
        SK: sk,
      },
    );

    console.log('Picklist data ->', picklistData);

    if (!picklistData?.Item) {
      picklistData = await this.dynamoDBService.getObject(
        `gus-eip-picklist-${process.env.STAGE}`,
        {
          PK: 'en',
          SK: sk,
        },
      );
      if (!picklistData?.Item) {
        console.log(`No picklist data found for field: ${fieldName}`);
        return [];
      }

      console.log('Picklist data ->', picklistData);

      const translatedPicklist = await Promise.all(
        picklistData?.Item?.data?.map(async (item) => ({
          label: item.label,
          value: item.value,
          displayText: await this.oapTranslationService.translateText(
            item.label,
            'en',
            displayLanguage,
          ),
        })),
      );

      console.log('TranslatedPicklist ->', translatedPicklist);

      await this.dynamoDBService.putObject(
        `gus-eip-picklist-${process.env.STAGE}`,
        {
          Item: {
            PK: pk,
            SK: sk,
            data: translatedPicklist,
          },
        },
      );

      return translatedPicklist;
    }

    return picklistData?.Item?.data;
  }

  async updateStudentDetails(
    PK: string,
    SK: string,
    attributes: Record<string, any>,
  ): Promise<any> {
    // Construct dynamic UpdateExpression, ExpressionAttributeNames, and ExpressionAttributeValues
    let updateExpression = 'SET ';
    const expressionAttributeNames = {};
    const expressionAttributeValues = {};

    for (const [key, value] of Object.entries(attributes)) {
      updateExpression += `#${key} = :${key}, `;
      expressionAttributeNames[`#${key}`] = key;
      expressionAttributeValues[`:${key}`] = value;
    }

    // Remove the trailing comma and space from UpdateExpression
    updateExpression = updateExpression.slice(0, -2);

    const updateParams = {
      TableName: process.env.GUS_OAP_STUDENT_DETAILS_TABLE,
      Key: {
        PK: PK,
        SK: SK,
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'UPDATED_NEW',
    };

    const response = await this.dynamoDBService.updateObject(updateParams);
    return response;
  }

  async updateAndGetAppId(PK, brandCode): Promise<any> {
    const updateParams = {
      TableName: process.env.OAP_BRAND_TABLE,
      Key: {
        PK: PK,
      },
      UpdateExpression:
        'SET latestStudentId = if_not_exists(latestStudentId, :start) + :inc',
      ExpressionAttributeValues: {
        ':inc': 1,
        ':start': 0,
      },
      ReturnValues: 'UPDATED_NEW',
    };
    try {
      const response = await this.dynamoDBService.updateObject(updateParams);
      let studentId = '0000000000';
      studentId = String(
        Number(studentId) + response.Attributes.latestStudentId,
      ).padStart(10, '0');
      return `${brandCode}${studentId}`;
    } catch (error) {
      throw error;
    }
  }
  async updateAppIdOldRecord(request): Promise<any> {
    try {
      return await this.utilityService.createAppIdForExistingApplications(
        request,
      );
    } catch (error) {
      throw error;
    }
  }

  async makeApiCalls(payloads: any[]): Promise<any[]> {
    const url =
      'https://oap-dev-api.apphero.io/oap/savestudentdetails?oapName=HZU&mode=AGENT';
    const headers = {
      'Content-Type': 'application/json',
      'x-api-key': 'yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ',
    };

    const requests = payloads.map(async (payload) => {
      const config: AxiosRequestConfig = {
        headers,
      };

      try {
        const response = await axios.post(url, payload, config);
        return response.data;
      } catch (error) {
        console.error('Error making API call:', error);
        throw error;
      }
    });

    try {
      return await Promise.all(requests);
    } catch (error) {
      // Handle error
      console.error('Error making API calls:', error);
      throw error;
    }
  }

  async addStudentFormSectionsData(
    oapName: string,
    mode: string,
    // section: string
  ): Promise<any> {
    const params = {
      TableName: process.env.GUS_OAP_FORM_SECTION_TABLE,
      KeyConditionExpression: 'PK = :pkValue',
      ExpressionAttributeValues: {
        ':pkValue': `${oapName}_${mode}`,
      },
    };

    try {
      const agentFormSectionDetails = await this.dynamoDBService.queryObjects(
        params,
      );

      if (agentFormSectionDetails.Items) {
        agentFormSectionDetails.Items.forEach(async (item) => {
          item.PK = `${oapName}_STUDENT`;

          item.fieldData.forEach((fieldDataItem) => {
            if (fieldDataItem.nextSection) {
              fieldDataItem.nextSection.mode = 'STUDENT';
            }
          });

          const response = await this.dynamoDBService.putObject(
            process.env.GUS_OAP_FORM_SECTION_TABLE,
            {
              Item: {
                ...item,
              },
            },
          );
        });
      } else {
        throw new Error('No record found for provided OAP and mode');
      }
      const queryFormsParams = {
        TableName: process.env.GUS_OAP_FORM_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        ExpressionAttributeValues: {
          ':pkValue': `${oapName}_${mode}`,
        },
      };

      const agentFormDetailsResponse = await this.dynamoDBService.queryObjects(
        queryFormsParams,
      );

      if (agentFormDetailsResponse.Items) {
        agentFormDetailsResponse.Items.forEach(async (item) => {
          item.PK = `${oapName}_STUDENT`;
          item.oap = `${oapName}_STUDENT`;
          item.mode = 'STUDENT';

          await this.dynamoDBService.putObject(process.env.GUS_OAP_FORM_TABLE, {
            Item: {
              ...item,
            },
          });
        });
      } else {
        throw new Error('No record found for provided OAP and mode');
      }
      return agentFormSectionDetails.Items;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async createLead(oapName: string, studentDetails, APIKEY): Promise<any> {
    try {
      const lead = await this.salesforceService.fetchData(
        `gus/lead/${encodeURIComponent(studentDetails.email)}`,
        APIKEY,
      );
      if (!lead.length) {
        const oapDetail = await this.getOapDetails(oapName, 'STUDENT');
        let privacyPolicyConsent = !studentDetails?.privacyPolicyConsent;
        const request = {
          FirstName: studentDetails?.firstName,
          Phone: studentDetails?.phoneNumber?.numberWithCode,
          Email: studentDetails?.email,
          LastName: studentDetails?.lastName,
          ApplicationSource__c: 'OAP',
          Distrbute_Automatically__c: true,
          Country: studentDetails?.country?.label,
          BusinessUnit__c: oapDetail?.Item?.businessUnit,
          Brand__c: oapDetail?.Item?.brand,
          Programme__c: oapDetail?.Item?.defaultProgram,
          OwnerId: oapDetail?.Item?.salesQueue,
          Vendor__c: 'OAP',
          pi__utm_source__c: studentDetails?.utmParams?.utmSource,
          pi__utm_medium__c: studentDetails?.utmParams?.utmMedium,
          pi__utm_campaign__c: studentDetails?.utmParams?.utmCampaign,
          pi__utm_content__c: studentDetails?.utmParams?.utmContent,
          pi__utm_term__c: studentDetails?.utmParams?.utmTerm,
          Utm_Referrer__c: studentDetails?.utmParams?.utmReferrer,
          utm_network__c: studentDetails?.utmParams?.utmNetwork,
          HasOptedOutOfMarketingEmailSync__c: privacyPolicyConsent,
          HasOptedOutOfGUSMarketingEmail__c: privacyPolicyConsent,
          Source_Website__c: this.getUtmWebsite(oapDetail?.Item?.brand),
        };

        if (studentDetails?.nationality) {
          request['Nationality__c'] = studentDetails?.nationality?.label;
        }
        console.log(request);
        await this.salesforceService.postData('gus/lead', request, APIKEY);
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async saveStudentInfo(
    oapName: string,
    studentDetails: Record<string, any>,
    APIKEY,
  ): Promise<any> {
    const currentUTC = new Date().toISOString();
    const studentDetail = await this.getStudentDetails(
      studentDetails.email,
      oapName,
    );
    try {
      await this.upsertLead(oapName, studentDetails, APIKEY);
      if (studentDetails?.firstName && studentDetails?.lastName) {
        studentDetails['applicantInitials'] =
          (studentDetails?.firstName?.[0] || '') +
          (studentDetails?.lastName?.[0] || '');
      }

      if (Object.keys(studentDetail).length === 0) {
        studentDetails['createdAt'] = currentUTC;
        await this.dynamoDBService.putObject(
          process.env.GUS_OAP_STUDENT_DETAILS_TABLE,
          {
            Item: {
              PK: studentDetails['email'],
              SK: oapName,
              ...studentDetails,
            },
          },
        );
        return 'Student Details Saved Successfully';
      } else {
        studentDetails['updatedAt'] = currentUTC;
        await this.updateStudentDetails(
          studentDetails.email,
          oapName,
          studentDetails,
        );
        return 'Student Details Updated Successfully';
      }
    } catch (error) {
      throw error;
    }
  }

  async updateLead(studentDetails, APIKEY): Promise<any> {
    try {
      const lead = await this.salesforceService.fetchData(
        `gus/lead/${encodeURIComponent(studentDetails.email)}`,
        APIKEY,
      );
      console.log('Lead ->', lead);
      if (lead.length > 0) {
        let privacyPolicyConsent = !studentDetails?.privacyPolicyConsent;
        console.log('privacyPolicyConsent ->', privacyPolicyConsent);
        const request = {
          HasOptedOutOfMarketingEmailSync__c: privacyPolicyConsent,
          HasOptedOutOfGUSMarketingEmail__c: privacyPolicyConsent,
        };
        const response = await this.salesforceService.postData(
          `gus/lead/${lead[0].Id}`,
          request,
          APIKEY,
        );
        console.log('Response ->', response);
        return response;
      }
    } catch (error) {
      console.log('Error ->', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async upsertLead(
    oapName: string,
    studentDetails: any,
    APIKEY: string,
  ): Promise<any> {
    try {
      const lead = await this.salesforceService.fetchData(
        `gus/lead/${encodeURIComponent(studentDetails.email)}`,
        APIKEY,
      );

      if (!lead.length) {
        return await this.createLead(oapName, studentDetails, APIKEY);
      } else {
        let privacyPolicyConsent = !studentDetails?.privacyPolicyConsent;

        const updatePayload = {
          pi__utm_source__c: studentDetails?.utmParams?.utmSource,
          pi__utm_medium__c: studentDetails?.utmParams?.utmMedium,
          pi__utm_campaign__c: studentDetails?.utmParams?.utmCampaign,
          pi__utm_content__c: studentDetails?.utmParams?.utmContent,
          pi__utm_term__c: studentDetails?.utmParams?.utmTerm,
          Utm_Referrer__c: studentDetails?.utmParams?.utmReferrer,
          utm_network__c: studentDetails?.utmParams?.utmNetwork,
          HasOptedOutOfMarketingEmailSync__c: privacyPolicyConsent,
          HasOptedOutOfGUSMarketingEmail__c: privacyPolicyConsent,
          Vendor__c: 'OAP'
        };
        const oapDetail = await this.getOapDetails(oapName, 'STUDENT');

        // Only add UTM website if utmParams exists
        if (studentDetails?.utmParams) {
          updatePayload['Source_Website__c'] = this.getUtmWebsite(
            oapDetail.Item.brand,
          );
        }
        // If lead status is Prospect, update to New
        if (lead[0]?.Status === 'Prospect') {
          updatePayload['Status'] = 'New';
          updatePayload['OwnerId'] = oapDetail?.Item?.salesQueue;
        }
        if (updatePayload) {
          return await this.salesforceService.postData(
            `gus/lead/${lead[0].Id}`,
            updatePayload,
            APIKEY,
          );
        }
      }
    } catch (error) {
      console.log('Error in upsertLead ->', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getStudentInfo(oapName: string, email: string): Promise<any> {
    try {
      const studentInfo = await this.dynamoDBService.getObject(
        process.env.GUS_OAP_STUDENT_DETAILS_TABLE,
        {
          PK: email,
          SK: oapName,
        },
      );
      return studentInfo.Item;
    } catch (error) {
      throw error;
    }
  }
  async saveOapApplicantDetails(
    oapName: string,
    mode: string,
    applicantDetails: Record<string, any>,
    request,
    APIKEY: string,
  ): Promise<any> {
    const currentUTC = new Date().toISOString();
    try {
      const oapDetail = await this.getOapDetails(oapName, mode);
      applicantDetails['brand'] = oapDetail?.Item?.brand;
      applicantDetails['applicationSource'] = oapName;
      const firstLetter = applicantDetails?.firstName?.charAt(0).toUpperCase();
      const secondLetter = applicantDetails?.lastName?.charAt(0).toUpperCase();
      applicantDetails['fullName'] =
        firstLetter +
        applicantDetails?.firstName?.slice(1) +
        ' ' +
        secondLetter +
        applicantDetails?.lastName?.slice(1);
      applicantDetails['applicantInitials'] =
        (firstLetter || '') + (secondLetter || '');
      applicantDetails['sourceWebsite'] = this.getUtmWebsite(
        oapDetail?.Item?.brand,
      );

      if (applicantDetails?.applicationId) {
        applicantDetails['updatedAt'] = currentUTC;
        await this.log(
          applicantDetails?.applicationId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.UPDATE_APPLICATION,
          this.loggerEnum.UseCase[`${applicantDetails.sectionLabel}`],
          applicantDetails,
          applicantDetails,
          'Update application initialization',
          request?.brand,
          applicantDetails?.email || '',
          'Opportunity_Application_Account',
          applicantDetails?.applicationId,
        );
      } else {
        applicantDetails['applicationId'] = uuidv4();
        applicantDetails['appId'] = await this.updateAndGetAppId(
          oapDetail?.Item?.brand,
          oapDetail?.Item?.brandCode,
        );
        applicantDetails = await this.buildCreateApplicationRequest(
          oapDetail?.Item,
          applicantDetails,
          mode,
        );
        applicantDetails['createdAt'] = currentUTC;
        await this.log(
          applicantDetails?.applicationId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.NEW_APPLICATION,
          this.loggerEnum.UseCase[`${applicantDetails.sectionLabel}`],
          applicantDetails,
          applicantDetails,
          'Create application initialization',
          request?.brand,
          applicantDetails?.email || '',
          'Opportunity_Application_Account',
          applicantDetails?.applicationId,
        );
      }
      let form;
      if (applicantDetails.applicationType) {
        form = applicantDetails?.applicationType.toUpperCase();
        form = `${form}_OAP`;
      } else {
        form = 'APPLICATION';
      }
      let oapSections;
      if (form) {
        oapSections = await this.getSections(oapName, form, mode);
      }
      if (oapSections && oapSections.length > 0) {
        const { progressPercentage, sections } =
          await this.calculateCompleteness(oapSections, applicantDetails);
        applicantDetails['progress'] = progressPercentage;
        applicantDetails['sections'] = sections;
      }
      let documents;
      if (applicantDetails.applicationStatus === 'submitted') {
        await this.submitApplication(
          applicantDetails,
          oapName,
          mode,
          oapDetail,
          request,
        );
        documents = await this.getAllDocs(oapName, applicantDetails);
        applicantDetails['stage'] = oapDetail?.Item?.onSubmissionStage;
        applicantDetails['admissionStage'] = 'Application submitted';
        applicantDetails['isSubmitted'] = true;
        await this.log(
          applicantDetails?.applicationId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.SUBMIT_APPLICATION,
          this.loggerEnum.UseCase[`${applicantDetails.sectionLabel}`],
          applicantDetails,
          applicantDetails,
          'Application submitted successfully',
          request?.brand,
          applicantDetails?.email || '',
          'Opportunity_Application_Account',
          applicantDetails?.applicationId,
        );
      }

      await this.dynamoDBService.putObject(process.env.STUDENT_DETAILS, {
        Item: {
          PK: applicantDetails['email'],
          SK: `${oapName}_${applicantDetails.applicationId}`,
          ...applicantDetails,
        },
      });
      applicantDetails['documents'] = documents ? documents : null;
      const messageAttributes = {
        source: {
          DataType: 'String',
          StringValue: `${oapName}`,
        },
      };
      if (
        !('metEligibilityCreteria' in applicantDetails) ||
        applicantDetails.metEligibilityCreteria
      ) {
        let messageDetails;
        const snsMessage = { ...applicantDetails };
        snsMessage['requestId'] = request?.requestId;
        snsMessage['APIKEY'] = APIKEY;
        messageDetails = await this.publishMessageToSNS(
          JSON.stringify(snsMessage),
          process.env.OAP_SF_TOPIC_ARN,
          messageAttributes,
          applicantDetails.applicationId,
        );
        await this.log(
          applicantDetails?.applicationId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.POST_APPLICATION_DETAILS_TO_SNS,
          this.loggerEnum.UseCase[`${applicantDetails.sectionLabel}`],
          applicantDetails,
          applicantDetails,
          'Application saved to SNS',
          request?.brand,
          applicantDetails?.email || '',
          'Opportunity_Application_Account',
          applicantDetails?.applicationId,
        );
      } else {
        await this.log(
          applicantDetails?.applicationId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.OPERATION_COMPLETED,
          this.loggerEnum.UseCase[`${applicantDetails.sectionLabel}`],
          applicantDetails,
          applicantDetails,
          `Applicant does not meet the eligibility criteria to study at ${request?.brand}`,
          request?.brand,
          applicantDetails?.email || '',
          'Opportunity_Application_Account',
          applicantDetails?.applicationId,
        );
      }
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        request.requestId,
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Event.POST_APPLICATION_DETAILS_TO_SNS,
        this.loggerEnum.UseCase[`${applicantDetails.sectionLabel}`],
        applicantDetails,
        applicantDetails,
        error,
        request?.brand,
        applicantDetails?.email || '',
        `oap-backend/${applicantDetails.applicationId}/${request.requestId}`,
        'Application_Form_Id__c',
        applicantDetails.applicationId,
        'Opportunity_Application_Account',
        applicantDetails?.applicationId,
      );
      throw error;
    }
    return applicantDetails;
  }

  async uploadOpportunityFiles(fileData, request): Promise<any> {
    const currentUTC = new Date().toISOString();
    const buffer = Buffer.from(fileData?.base64File, 'base64');
    const bucketName = fileData.bucketName;
    const objectKey = fileData.applicationFormId
      ? `${fileData.applicationFormId}/${fileData?.documentType
      }/${uuidv4()}.${await this.getFileExtension(fileData?.fileName)}`
      : `${fileData.opportunityId}/${fileData?.documentType
      }/${uuidv4()}.${await this.getFileExtension(fileData?.fileName)}`;

    try {
      const s3 = await this.s3Service.getS3CredentialsByRole(
        process.env.S3_CROSS_ACCESS_ROLE_ARN,
      );
      const params = {
        Bucket: bucketName,
        Key: objectKey,
        Body: buffer,
      };
      return await s3.upload(params).promise();
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        request.requestId,
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Event.UPLOAD_DOCUMENT,
        this.loggerEnum.UseCase.UPLOAD_DOCUMENT,
        fileData,
        {
          Bucket: bucketName,
          Key: objectKey,
          Body: buffer,
        },
        error,
        request?.brand,
        '',
        `oap-backend/${fileData.applicationFormId}/${request.requestId}`,
        'Application_Form_Id__c',
        fileData.applicationFormId,
        'Opportunity_Application_Account',
        fileData.applicationFormId,
      );
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }
  async submitApplication(
    applicantDetails,
    oapName,
    mode,
    oapDetail,
    request,
  ): Promise<any> {
    let form;
    let bucketName;
    if (applicantDetails.applicationType) {
      form = applicantDetails?.applicationType.toUpperCase();
      form = `${form}_OAP`;
    } else {
      form = 'APPLICATION';
    }
    const sections = await this.getSections(oapName, form, mode);
    const pdfRequest = await this.buildPdfGenRequest(
      sections,
      applicantDetails,
      oapDetail.Item,
    );
    const submittedApplication = await this.externalService.postData(
      pdfRequest,
      process.env.PDF_REQUEST_PATH,
    );
    const name = `${applicantDetails?.applicationId}.pdf`;
    const type = 'Application form';
    const documentId = uuidv4();
    const objectKey = `${applicantDetails?.applicationId}/${type}/${documentId}.pdf`;
    switch (applicantDetails?.brand) {
      case 'UEG':
        bucketName = process.env.CAMPUSNET_BUCKET_NAME;
        break;
      default:
        bucketName = process.env.REVIEW_CENTER_BUCKET_NAME;
    }
    const signedUrl = await this.buildUploadFileSignedUrl(
      bucketName,
      objectKey,
      process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
      'application/pdf',
    );
    await this.externalService.putData(
      Buffer.from(submittedApplication.pdfBase64, 'base64'),
      signedUrl,
      {
        'Content-Type': 'application/pdf',
      },
    );
    const docDetails = {
      applicationId: applicantDetails.applicationId,
      oapName: oapName,
      email: applicantDetails.email,
      documentType: type,
      documentFormat: 'pdf',
      documentName: name,
      contentType: 'application/pdf',
      path: objectKey,
      documentId: documentId,
      bucketName: bucketName,
    };
    await this.uploadDocToDb(docDetails, request);
    let additionalAttachments;
    if (oapName === 'HZU') {
      additionalAttachments = await this.generatePdf(
        oapName,
        applicantDetails,
        request,
      );
    }
    const messageAttributes = {
      source: {
        DataType: 'String',
        StringValue: oapName,
      },
    };
    oapDetail.Item.submissionEmailDetails.emailAddress = applicantDetails.email;
    oapDetail.Item.submissionEmailDetails.firstName =
      applicantDetails?.firstName;
    oapDetail.Item.submissionEmailDetails.lastName = applicantDetails?.lastName;
    oapDetail.Item.submissionEmailDetails.brand = applicantDetails?.brand;
    oapDetail.Item.submissionEmailDetails.applicationId =
      applicantDetails?.applicationId;
    oapDetail.Item.submissionEmailDetails.businessUnitFilter =
      oapDetail.Item?.businessUnitFilter;
    oapDetail.Item.submissionEmailDetails.documentId = documentId;
    oapDetail.Item.submissionEmailDetails.intakeDate =
      applicantDetails.intakeDisplayName;
    oapDetail.Item.submissionEmailDetails.programmeName =
      applicantDetails.programDisplayName;
    oapDetail.Item.submissionEmailDetails.additionalAttachments = [];
    oapDetail.Item.submissionEmailDetails.additionalAttachments =
      additionalAttachments || [];
    if (!applicantDetails.crApp) {
      await this.publishMessageToSNS(
        JSON.stringify(oapDetail.Item.submissionEmailDetails),
        process.env.OAP_NOTIFICATION_TOPIC_ARN,
        messageAttributes,
        null,
      );
    }
  }
  async generatePdf(oapName, applicantDetails, request): Promise<any> {
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const year = date.getFullYear();
      return `${month}/${day}/${year}`;
    };

    const baseRequest = {
      initial: applicantDetails?.applicantInitials,
      fullName: applicantDetails?.fullName,
      email: applicantDetails?.email,
      signature: applicantDetails?.submissionSignature,
      date: applicantDetails.submittedDate
        ? formatDate(applicantDetails.submittedDate)
        : undefined,
      DOB: applicantDetails.birthDate
        ? formatDate(applicantDetails.birthDate)
        : undefined,
      programName: applicantDetails?.programDisplayName,
      intake: applicantDetails.intake
        ? formatDate(applicantDetails.intake)
        : undefined,
      aggreditationSignature: applicantDetails?.aggreditationSignature,
      aggreditationDate: applicantDetails.aggreditationDate
        ? formatDate(applicantDetails.aggreditationDate)
        : undefined,
      consumerInfosignature: applicantDetails?.consumerInfosignature,
      consumerInfodate: applicantDetails.consumerInfodate
        ? formatDate(applicantDetails?.consumerInfodate)
        : undefined,
      courseUniqueId: applicantDetails?.courseUniqueId,
      level: applicantDetails?.programTypeDisplayName,
      duration: applicantDetails?.duration,
      semesters: applicantDetails?.semesters,
      totalSemesterCreditHours: applicantDetails?.totalSemesterCreditHours,
      graduationDate: applicantDetails?.graduationDate
        ? formatDate(applicantDetails?.graduationDate)
        : undefined,
      enrollmentFee: applicantDetails?.enrollmentFee,
      learnerResourceFee: applicantDetails?.learnerResourceFee,
      feeReduction: applicantDetails.feeReduction,
      perSemesterProgramFee: applicantDetails?.perSemesterProgramFee,
      tuitionRatePerCredit: applicantDetails?.tuitionRatePerCredit,
      totalProgramCost: applicantDetails?.totalProgramCost,
      emergencyContactName: `${applicantDetails?.emergencyContactFirstName} ${applicantDetails?.emergencyContactLastName}`,
    };

    const mappings = {
      AGGREDITATION_FORM: 'Accreditation Disclosure Form',
      CONSUMER_INFO_FORM: 'Consumer Information Disclosure Form',
      INTERNATIONAL_STUDENT_STATEMENT:
        'International Student Statement of Understanding',
      ENROLLMENT_AGREEMENT_FORM: 'Enrollment Agreement',
    };

    try {
      const pdfPromises = Object.entries(mappings).map(
        async ([event, documentType]) => {
          const pdfRequest = {
            ...baseRequest,
            event,
          };

          // Get PDF base64 from external API
          const response = await this.externalService.postData(
            pdfRequest,
            `${process.env.OAP_BACKEND_PATH}oap/generateenrollmentpdf`,
          );

          const documentId = uuidv4();
          const name = `${documentId}.pdf`;
          const objectKey = `${applicantDetails?.applicationId}/${documentType}/${documentId}.pdf`;
          let bucketName;
          switch (applicantDetails.brand) {
            case 'UEG':
              bucketName = process.env.CAMPUSNET_BUCKET_NAME;
              break;
            default:
              bucketName = process.env.REVIEW_CENTER_BUCKET_NAME;
          }
          // Get signed URL for S3 upload
          const signedUrl = await this.buildUploadFileSignedUrl(
            bucketName,
            objectKey,
            process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
            'application/pdf',
          );

          // Upload PDF to S3
          await this.externalService.putData(
            Buffer.from(response.pdfBase64, 'base64'),
            signedUrl,
            {
              'Content-Type': 'application/pdf',
            },
          );

          // Save document details to database
          const docDetails = {
            applicationId: applicantDetails.applicationId,
            oapName: oapName,
            email: applicantDetails.email,
            documentType: documentType,
            documentFormat: 'pdf',
            documentName: name,
            contentType: 'application/pdf',
            path: objectKey,
            documentId: documentId,
          };

          await this.uploadDocToDb(docDetails, request);

          return { objectKey, documentId };
        },
      );

      const objectKeys = await Promise.all(pdfPromises);
      console.log('objectKeys', JSON.stringify(objectKeys));
      return objectKeys;
    } catch (error) {
      console.log(error);
      throw new HttpException(
        'Error generating PDFs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async buildCreateApplicationRequest(
    oapDetails,
    applicantDetails,
    mode,
  ): Promise<any> {
    if (mode.toLowerCase() === 'agent') {
      applicantDetails['leadSource'] = 'Agent';
      applicantDetails['opportunityApplicationSource'] = 'Agent Portal';
      applicantDetails['applicationFilledBy'] = 'agent';
    } else if (mode.toLowerCase() === 'student') {
      applicantDetails['opportunityApplicationSource'] = 'OAP';
      applicantDetails['applicationFilledBy'] = 'student';
      applicantDetails['distributeAutomatic'] = true;
    }
    if (oapDetails?.recordTypeIds) {
      for (const key in oapDetails?.recordTypeIds) {
        applicantDetails[`${key}RecordTypeId`] = oapDetails?.recordTypeIds[key];
      }
    }
    try {
      applicantDetails['pricebookId'] = await this.getPriceBookId(
        oapDetails,
        applicantDetails,
      );
      applicantDetails['teamMembers'] = this.buildTeamMembersDetail(
        applicantDetails,
        oapDetails,
      );
      applicantDetails['stage'] = 'Application';
      applicantDetails['admissionStage'] = 'Draft Application';
      applicantDetails['businessUnitFilter'] = oapDetails?.brand;
      applicantDetails['brand'] = oapDetails?.brand;
      applicantDetails['businessUnit'] = oapDetails?.businessUnit;
      return applicantDetails;
    } catch (error) {
      throw error;
    }
  }
  buildTeamMembersDetail(applicantDetails: any, oapDetails: any): any[] {
    const mappingUserWithRoles: { [key: string]: string } = {
      agentContactUserId: 'Agent Contact',
      accountManagerUserId: 'Business Developer',
    };

    const response = [];

    try {
      for (const user in mappingUserWithRoles) {
        if (applicantDetails && applicantDetails[user]) {
          response.push({
            userId: applicantDetails[user],
            teamMemberRole: mappingUserWithRoles[user],
            opportunityAccessLevel:
              oapDetails?.opportunityAccessLevel || 'Edit',
          });
        } else {
          console.error(
            `Error: ${mappingUserWithRoles[user]} details not found.`,
          );
        }
      }
    } catch (error) {
      console.error('An error occurred:', error);
    }
    return response;
  }
  async getPriceBookId(oapDetails, applicantDetails): Promise<string> {
    switch (oapDetails['PK'].toLowerCase()) {
      case 'lim':
        return await this.limPriceBookSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
      case 'ibat_el':
        return await this.ibatElPriceBookSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
      case 'hzu':
        return await this.hzuPriceBookSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
      case 'ucw':
        return await this.ucwAgentPriceBoolSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
      case 'unfc':
        return await this.unfcAgentPriceBookSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
      case 'lsbfmyr':
        return await this.lsbfmyrPriceBookSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
      default:
        return await this.defaultPriceBookSelector.getPriceBook({
          oapDetails,
          applicantDetails,
        });
    }
  }
  flattenObject(obj, parentKey = '') {
    try {
      const result = {};

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const newKey = key;

          if (typeof obj[key] === 'object' && obj[key] !== null) {
            Object.assign(result, this.flattenObject(obj[key], newKey));
          } else {
            result[newKey] = obj[key];
          }
        }
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  async getLegacyApplicationOapDetails(
    oapName,
    mode,
    programId,
    termId,
    apikey,
    request,
  ): Promise<any> {
    try {
      let legacyData;
      const oapDetails = await this.getOapDetails(oapName, mode);
      const programmeDetails = await this.lookUpService.getProgrammes(
        apikey,
        request,
      );
      let program;
      if (programmeDetails) {
        program = programmeDetails?.find(
          (program) => program.value === programId,
        );
      } else {
        throw new Error(`Program details not found for the ${programId}`);
      }
      const pricebookId = await this.getPriceBookId(oapDetails.Item, request);
      const programIntakeDetails =
        await this.lookUpService.getIntakeByProgrammeId(
          programId,
          apikey,
          request,
        );
      let intakeDetail;
      if (programIntakeDetails) {
        intakeDetail = programIntakeDetails?.find(
          (intakeData) => (intakeData.id = termId),
        );
      } else {
        throw new Error(
          `Program intake details not found for the ${programId}`,
        );
      }
      legacyData = {
        pricebookId: pricebookId,
        productId: intakeDetail?.productId,
        startTerm: intakeDetail?.value,
        startTermDisplayName: intakeDetail?.label,
        levelName: program?.level,
        level: program?.levelId,
        programName: program?.label,
      };
      if (oapDetails?.Item?.recordTypeIds) {
        for (const key in oapDetails?.Item?.recordTypeIds) {
          legacyData[`${key}RecordTypeId`] =
            oapDetails?.Item?.recordTypeIds[key];
        }
      }
      return legacyData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async calculateCompleteness(oapSections, studentDetails): Promise<any> {
    try {
      let totalRequiredFieldCount = 0;
      let totalRequiredFieldCompletedCount = 0;
      const sectionProgress = [];
      for (const section of oapSections) {
        let sectionRequiredFieldCount = 0;
        let sectionRequiredFieldCompletedCount = 0;
        if (await this.shouldDisplayItem(section, studentDetails)) {
          for (const field of section.fieldData) {
            if (
              field.type != 'subsection' &&
              field.required &&
              (await this.shouldDisplayItem(field, studentDetails))
            ) {
              totalRequiredFieldCount++;
              sectionRequiredFieldCount++;
              const fieldValue = studentDetails[field.fieldName];
              if (fieldValue !== undefined && fieldValue !== null) {
                totalRequiredFieldCompletedCount++;
                sectionRequiredFieldCompletedCount++;
              }
              // else {
              //   console.log('ffwee2e', field.fieldName);
              // }
            } else if (
              field.type === 'subsection' &&
              (await this.shouldDisplayItem(field, studentDetails))
            ) {
              if (field.sectionCanRepeat) {
                let sectionMinRequiredLength = field.minLength;
                if (
                  field.minLengthWhen &&
                  studentDetails[field.minLengthWhen.fieldName] ===
                  field.minLengthWhen.value
                ) {
                  sectionMinRequiredLength = field.minLengthWhen.minLength;
                }
                const sectionStudentDetails = studentDetails[field.subSection];
                const requiredSubFields = [];
                if (
                  sectionMinRequiredLength > 0 ||
                  sectionStudentDetails?.length
                ) {
                  for (const subFields of field[field.subSection].fieldData) {
                    if (subFields.required) {
                      requiredSubFields.push(subFields.fieldName);
                    }
                  }
                  for (let i = 0; i < sectionStudentDetails?.length; i++) {
                    for (const subFields of field[field.subSection].fieldData) {
                      if (
                        subFields.required &&
                        (await this.shouldDisplayItem(
                          subFields,
                          sectionStudentDetails[i],
                        ))
                      ) {
                        totalRequiredFieldCount++;
                        sectionRequiredFieldCount++;
                        const subFieldValue =
                          sectionStudentDetails?.[i]?.[subFields.fieldName];
                        if (
                          subFieldValue !== undefined &&
                          subFieldValue !== null
                        ) {
                          totalRequiredFieldCompletedCount++;
                          sectionRequiredFieldCompletedCount++;
                        }
                        //  else {
                        //   console.log('ffwe', field);
                        // }
                      }
                    }
                  }
                  if (
                    sectionStudentDetails?.length <= sectionMinRequiredLength &&
                    requiredSubFields?.length > 0
                  ) {
                    sectionRequiredFieldCount +=
                      (sectionMinRequiredLength -
                        sectionStudentDetails?.length) *
                      requiredSubFields?.length;
                    totalRequiredFieldCount +=
                      (sectionMinRequiredLength -
                        sectionStudentDetails?.length) *
                      requiredSubFields?.length;
                  }
                }
              } else {
                for (const subFields of field[field.subSection].fieldData) {
                  if (
                    subFields.required &&
                    (await this.shouldDisplayItem(subFields, studentDetails))
                  ) {
                    const subFieldValue = studentDetails[subFields.fieldName];
                    totalRequiredFieldCount++;
                    sectionRequiredFieldCount++;
                    if (subFieldValue !== undefined && subFieldValue !== null) {
                      totalRequiredFieldCompletedCount++;
                      sectionRequiredFieldCompletedCount++;
                    }
                    // else {
                    //   console.log('hereer', subFields.fieldName);
                    // }
                  }
                }
              }
            }
          }
          if (
            section.displayName != 'Review & Submit' ||
            studentDetails.applicationStatus === 'submitted'
          ) {
            sectionProgress.push({
              name: section.displayName,
              displayOrder: section.displayOrder,
              status:
                sectionRequiredFieldCompletedCount >= sectionRequiredFieldCount,
            });
          } else {
            sectionProgress.push({
              name: section.displayName,
              displayOrder: section.displayOrder,
              status: false,
            });
          }
        }
      }
      return {
        progressPercentage: this.calculatePercentage(
          totalRequiredFieldCompletedCount,
          totalRequiredFieldCount,
        ),
        sections: sectionProgress,
      };
    } catch (error) {
      console.error('An error occurred:', error);
      return {
        progressPercentage: 0,
        sections: [],
      };
    }
  }
  calculatePercentage(part, total) {
    if (total === 0) {
      return 0;
    }
    const percentage = (part / total) * 100;
    return Math.round(percentage);
  }
  async shouldDisplayItem(item, formFieldsValue) {
    const visibleWhen = item?.visibleWhen;
    if (visibleWhen?.rules && Array.isArray(visibleWhen.rules)) {
      const { condition = 'and', rules } = visibleWhen;

      const ruleResults = rules.map((rule) => {
        const fieldValue = formFieldsValue?.[rule.fieldName];
        if (fieldValue === undefined || fieldValue === null) return false;

        if (Array.isArray(rule.value)) {
          return rule.value.includes(fieldValue);
        } else {
          return fieldValue === rule.value;
        }
      });

      switch (condition) {
        case 'and':
          return ruleResults.every(Boolean);
        case 'or':
          return ruleResults.some(Boolean);
        case 'notAnd':
          return !ruleResults.every(Boolean);
        case 'notOr':
          return !ruleResults.some(Boolean);
        default:
          return true;
      }
    }
    const visibleConditions = Array.isArray(item?.visibleWhen)
      ? item.visibleWhen
      : [item?.visibleWhen];

    for (const condition of visibleConditions) {
      const { fieldName, value, condition: conditionType } = condition || {};
      if (fieldName) {
        const fieldValue = formFieldsValue?.[fieldName];

        if (fieldValue === undefined || fieldValue === null) {
          return false;
        }

        if (Array.isArray(value)) {
          // Handle notEqual condition
          if (conditionType === 'notEqual') {
            if (value.some((v) => v.value === fieldValue || v === fieldValue)) {
              return false;
            }
          } else {
            if (!value.some((v) => v.value === fieldValue)) {
              return false;
            }
          }
          if (conditionType === 'equal') {
            if (value.includes(fieldValue)) {
              return true;
            }
          }
        } else {
          if (conditionType === 'notEqual') {
            if (fieldValue === value) {
              return false;
            }
          } else {
            if (fieldValue !== value) {
              return false;
            }
          }
        }
      }
    }
    return true;
  }

  async buildGetFileSignedUrl(bucket, path, roleArn): Promise<any> {
    try {
      const s3 = await this.s3Service.getS3CredentialsByRole(roleArn);
      const params = {
        Bucket: bucket,
        Key: path,
        Expires: 3600,
      };
      const filePath = await new Promise((resolve, reject) => {
        s3.getSignedUrl('getObject', params, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });
      return filePath;
    } catch (error) {
      throw error;
    }
  }
  async buildUploadFileSignedUrl(
    bucket,
    path,
    roleArn,
    contentType,
  ): Promise<any> {
    try {
      const s3 = await this.s3Service.getS3CredentialsByRole(roleArn);
      const params = {
        Bucket: bucket,
        Key: path,
        Expires: 3600,
        ContentType: contentType,
        ACL: 'public-read',
      };
      const filePath = await new Promise((resolve, reject) => {
        s3.getSignedUrl('putObject', params, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });
      return filePath;
    } catch (error) {
      throw error;
    }
  }
  urlEncode(value: string | null): string {
    if (value === null) {
      return '';
    }

    let encoded: string = encodeURIComponent(value);
    encoded = encoded.replace(/\(/g, '%28');
    encoded = encoded.replace(/\)/g, '%29');
    encoded = encoded.replace(/\+/g, '%20');
    encoded = encoded.replace(/\*/g, '%2A');
    encoded = encoded.replace(/%7E/g, '~');
    if (encoded.includes('%2F')) {
      encoded = encoded.replace(/%2F/g, '/');
    }
    return encoded;
  }

  private getBucketNameByBrand(brand: string): string {
    switch (brand) {
      case 'UEG':
        return process.env.CAMPUSNET_BUCKET_NAME;
      default:
        return process.env.REVIEW_CENTER_BUCKET_NAME;
    }
  }

  async getSignedUrlToUploadDoc(docDetails, request): Promise<any> {
    const currentUTC = new Date().toISOString();
    try {
      const objectKey = `${docDetails.applicationId}/${docDetails?.documentType
        }/${docDetails.documentId}.${await this.getFileExtension(
          docDetails?.documentName,
        )}`;
      console.log('Document details -->', docDetails);

      const bucketName = this.getBucketNameByBrand(
        docDetails?.brand || request?.brand,
      );

      return {
        signedUrl: await this.buildUploadFileSignedUrl(
          bucketName,
          objectKey,
          process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
          docDetails.contentType,
        ),
      };
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        request.requestId,
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Event.UPLOAD_DOCUMENT,
        this.loggerEnum.UseCase.UPLOAD_DOCUMENT,
        docDetails,
        docDetails,
        error,
        request?.brand,
        '',
        `oap-backend/${docDetails.applicationId}/${request.requestId}`,
        'Application_Form_Id__c',
        docDetails.applicationId,
        'Opportunity_Application_Account',
        docDetails.applicationId,
      );
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }
  async getFileExtension(fileName): Promise<string> {
    const parts = fileName.split('.');
    if (parts.length === 1) {
      return '';
    }
    return parts[parts.length - 1];
  }
  async uploadDocToDb(docDetails, request): Promise<any> {
    try {
      const inputDocDetails = docDetails;
      const objectKey = `${docDetails.applicationId}/${docDetails?.documentType
        }/${docDetails?.documentId}.${await this.getFileExtension(
          docDetails?.documentName,
        )}`;
      docDetails['path'] = objectKey;
      const currentUTC = new Date().toISOString();
      docDetails['createdAt'] = currentUTC;
      if (docDetails.existingObjectKey) {
        const bucketNameToUse = docDetails.bucketName
          ? docDetails.bucketName
          : this.getBucketNameByBrand(docDetails.brand);
        try {
          await this.s3Service.copyObject(
            bucketNameToUse,
            docDetails.existingObjectKey,
            objectKey,
            process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
          );
        } catch (error) {
          await this.cloudWatchLoggerService.error(
            request.requestId,
            currentUTC,
            this.loggerEnum.Component.OAP_BACKEND,
            this.loggerEnum.Component.OAP_FRONTEND,
            this.loggerEnum.Component.OAP_HANDLERS,
            this.loggerEnum.Event.UPLOAD_DOCUMENT,
            this.loggerEnum.UseCase.UPLOAD_DOCUMENT,
            inputDocDetails,
            {
              bucketName: bucketNameToUse,
              sourceKey: docDetails.existingObjectKey,
              destinationKey: objectKey,
            },
            error,
            request?.brand,
            docDetails.email,
            `oap-backend/${docDetails.applicationId}/${request.requestId}`,
            'Application_Form_Id__c',
            docDetails.applicationId,
            'Opportunity_Application_Account',
            docDetails.applicationId,
          );
          throw new Error('Failed to copy existing object in S3');
        }
      }
      const sk = docDetails?.documentId
        ? `${docDetails['oapName']}_${docDetails.applicationId}_${docDetails.documentType}_${docDetails?.documentId}`
        : `${docDetails['oapName']}_${docDetails.applicationId}_${docDetails.documentType}`;
      await this.dynamoDBService.putObject(process.env.STUDENT_DOCUMENTS, {
        Item: {
          PK: docDetails['email'],
          SK: sk,
          ...docDetails,
        },
      });
      if (docDetails?.processOcr) {
        return await this.extractIdentityInfoDetails(
          docDetails,
          objectKey,
          request,
        );
      }
      await this.log(
        docDetails?.applicationId,
        request.requestId,
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Event.UPLOAD_DOCUMENT,
        this.loggerEnum.UseCase.UPLOAD_DOCUMENT,
        inputDocDetails,
        docDetails,
        'Document upload success',
        request?.brand,
        docDetails['email'],
        'Opportunity_Application_Account',
        docDetails.applicationId,
      );
    } catch (error) {
      throw error;
    }
  }
  private async logOcrEvent(
    event: string,
    docDetails: any,
    request: any,
    additionalData?: any,
  ): Promise<void> {
    const currentUTC = new Date().toISOString();
    const baseLogData = {
      applicationId: docDetails?.applicationId,
      requestId: request?.requestId,
      timestamp: currentUTC,
      component: this.loggerEnum.Component.OAP_BACKEND,
      source: this.loggerEnum.Component.OAP_FRONTEND,
      destination: this.loggerEnum.Component.OCR,
      useCase: this.loggerEnum.UseCase.OCR_REQUEST_PROCESSING,
      brand: request?.businessUnitFilter,
      email: docDetails?.email || '',
      destinationType: 'Opportunity_Application_Account',
      destinationId: docDetails?.applicationId,
    };

    switch (event) {
      case 'OCR_STARTED':
        await this.log(
          baseLogData.applicationId,
          baseLogData.requestId,
          baseLogData.timestamp,
          baseLogData.component,
          baseLogData.source,
          baseLogData.destination,
          this.loggerEnum.Event.OCR_PROCESS_STARTED,
          baseLogData.useCase,
          docDetails,
          { status: 'started', ...additionalData },
          'OCR processing started',
          baseLogData.brand,
          baseLogData.email,
          baseLogData.destinationType,
          baseLogData.destinationId,
        );
        break;

      case 'OCR_COMPLETED':
        await this.log(
          baseLogData.applicationId,
          baseLogData.requestId,
          baseLogData.timestamp,
          baseLogData.component,
          baseLogData.source,
          baseLogData.destination,
          this.loggerEnum.Event.OCR_PROCESS_COMPLETED,
          baseLogData.useCase,
          docDetails,
          {
            status: 'completed',
            processingTime: additionalData?.processingTime,
          },
          'OCR processing completed successfully',
          baseLogData.brand,
          baseLogData.email,
          baseLogData.destinationType,
          baseLogData.destinationId,
        );
        break;

      case 'INVALID_OCR_RESPONSE':
      case 'MISSING_FIELDS':
      case 'OCR_ERROR':
        await this.cloudWatchLoggerService.error(
          baseLogData.requestId,
          baseLogData.timestamp,
          baseLogData.component,
          baseLogData.source,
          baseLogData.destination,
          this.loggerEnum.Event.OCR_PROCESS_ERROR,
          baseLogData.useCase,
          docDetails,
          additionalData,
          additionalData?.error || 'OCR processing error',
          baseLogData.brand,
          baseLogData.email,
          `oap-backend/${baseLogData.applicationId}/${baseLogData.requestId}`,
          'Application_Form_Id__c',
          baseLogData.applicationId,
          baseLogData.destinationType,
          baseLogData.destinationId,
        );
        break;
    }
  }
  async getCountryCode(countryName: string): Promise<string> {
    const countryMap = await this.dynamoDBService.getObject(
      `gus-eip-picklist-${process.env.STAGE}`,
      {
        PK: 'en',
        SK: 'countryMap',
      },
    );
    return countryMap?.Item?.data?.[countryName] || '';
  }
  async extractIdentityInfoDetails(
    docDetails,
    objectKey,
    request,
  ): Promise<any> {
    const startTime = performance.now();
    let signedUrlTime, ocrProcessTime, mappingTime;

    try {
      const bucketName = this.getBucketNameByBrand(
        docDetails?.businessUnitFilter,
      );

      // Start fetching country map in parallel with other operations
      const countryMapPromise = this.dynamoDBService.getObject(
        `gus-eip-picklist-${process.env.STAGE}`,
        {
          PK: 'en',
          SK: 'countryMap',
        },
      );

      const signedUrlStartTime = performance.now();
      const signedUrl = await this.buildS3SignedUrl(
        bucketName,
        objectKey,
        process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
      );
      signedUrlTime = performance.now() - signedUrlStartTime;

      const ocrRequest = {
        s3SignedUrl: signedUrl,
        ReferenceId: docDetails?.documentId,
        DocumentType: 'Passport',
      };
      // Log OCR process start
      await this.logOcrEvent('OCR_STARTED', docDetails, request, ocrRequest);

      const ocrStartTime = performance.now();
      const ocrResponse = await this.processOcrDocument(ocrRequest);
      ocrProcessTime = performance.now() - ocrStartTime;

      if (!ocrResponse?.data) {
        await this.logOcrEvent('INVALID_OCR_RESPONSE', docDetails, request, {
          error: 'Invalid response from OCR service',
          response: ocrResponse,
        });
        throw new HttpException(
          'Invalid response from OCR service',
          HttpStatus.UNPROCESSABLE_ENTITY,
        );
      }

      const mappingStartTime = performance.now();
      const countryMap = await countryMapPromise;
      ocrResponse.data.issuing_country_code =
        countryMap?.Item?.data?.[ocrResponse?.data?.issuing_country];
      const validationResult = this.validateOcrFields(ocrResponse.data);

      if (!validationResult.isValid) {
        await this.logOcrEvent('MISSING_FIELDS', docDetails, request, {
          error: 'Failed to extract all required passport fields',
          missingFields: validationResult.missingFields,
          ocrResponse: ocrResponse,
        });
        throw new HttpException(
          {
            message: 'Failed to extract all required passport fields',
            missingFields: validationResult.missingFields,
          },
          HttpStatus.UNPROCESSABLE_ENTITY,
        );
      }

      mappingTime = performance.now() - mappingStartTime;
      const totalTime = performance.now() - startTime;

      await this.logOcrEvent('OCR_COMPLETED', docDetails, request, {
        processingTime: {
          total: totalTime,
          signedUrl: signedUrlTime,
          ocrProcess: ocrProcessTime,
          mapping: mappingTime,
          ocrResponse: ocrResponse,
        },
      });

      const formatDate = (dateStr: string) => {
        const [day, month, year] = dateStr.split('/');
        return `${year}-${month}-${day}`;
      };

      return {
        passportFirstName: ocrResponse.data.first_name,
        passportLastName: ocrResponse.data.last_name,
        birthDate: formatDate(ocrResponse.data.date_of_birth),
        passportNumber: ocrResponse.data.document_number,
        passportExpiryDate: formatDate(ocrResponse.data.expiry_date),
        passportIssueDate: formatDate(ocrResponse.data.issue_date),
        passportIssuingCountryDisplayName: ocrResponse.data.issuing_country,
        passportIssuingCountry: ocrResponse.data.issuing_country_code,
        firstName: ocrResponse.data.first_name,
        lastName: ocrResponse.data.last_name,
        placeOfBirth: ocrResponse.data.place_of_birth,
      };
    } catch (error) {
      // Log any unexpected errors
      await this.logOcrEvent('OCR_ERROR', docDetails, request, {
        error: error.message,
        stack: error.stack,
        processingTime: {
          signedUrl: signedUrlTime,
          ocrProcess: ocrProcessTime,
          mapping: mappingTime,
        },
      });

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          message: 'Failed to process passport document',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  private validateOcrFields(ocrData: any): {
    isValid: boolean;
    missingFields: string[];
  } {
    const requiredFields = [
      'first_name',
      'last_name',
      'date_of_birth',
      'document_number',
      'issue_date',
      'issuing_country',
      'expiry_date',
      'issuing_country_code',
    ];

    const missingFields = requiredFields.filter(
      (field) =>
        !ocrData.hasOwnProperty(field) ||
        ocrData[field] === undefined ||
        ocrData[field] === null,
    );

    return {
      isValid: missingFields.length === 0,
      missingFields,
    };
  }

  async log(
    applicationId,
    requestedId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    logMessage,
    brand,
    secondaryKey,
    destinationObjectType,
    destinationObjectId,
    entityFieldName?,
  ): Promise<any> {
    await this.cloudWatchLoggerService.log(
      requestedId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      brand,
      secondaryKey,
      `oap-backend/${applicationId}/${requestedId}`,
      entityFieldName || 'Application_Form_Id__c',
      applicationId,
      destinationObjectType,
      destinationObjectId,
    );
  }
  async getStudentsDocuments(
    email,
    oapName,
    type,
    applicationId,
    documentId,
  ): Promise<any> {
    try {
      console.log('email', email, oapName, type, applicationId, documentId);
      let documents;
      if (documentId) {
        documents = await this.dynamoDBService.getObject(
          process.env.STUDENT_DOCUMENTS,
          {
            PK: email,
            SK: `${oapName}_${applicationId}_${type}_${documentId}`,
          },
        );
      } else {
        const params = {
          TableName: process.env.STUDENT_DOCUMENTS,
          KeyConditionExpression: 'PK = :pkValue and begins_with(SK, :skValue)',
          ExpressionAttributeValues: {
            ':pkValue': email,
            ':skValue': `${oapName}_${applicationId}_${type}`,
          },
        };
        const docs = await this.dynamoDBService.queryObjects(params);
        documents = docs.Items;
      }
      console.log('Documents', documents);
      return documents;
    } catch (error) {
      console.error('Error fetching student documents:', error);
      throw error;
    }
  }
  async getS3FileData(fileKey, s3BucketName = null, request): Promise<any> {
    try {
      console.log('File key', fileKey);
      const key = decodeURIComponent(fileKey);
      console.log('Key', key);
      return await this.getBase64File(
        s3BucketName ? s3BucketName : process.env.REVIEW_CENTER_BUCKET_NAME,
        key,
        process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
      );
    } catch (error) {
      console.error('Error fetching file from S3:', error);
    }
  }
  async deleteFile(
    email,
    oapName,
    type,
    applicationId,
    name,
    documentId,
  ): Promise<any> {
    const objectKey = `${applicationId}/${type}/${name}`;
    const s3 = await this.s3Service.getS3CredentialsByRole(
      process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
    );
    let bucketName;
    switch (oapName) {
      case 'UEG':
        bucketName = process.env.CAMPUSNET_BUCKET_NAME;
        break;
      default:
        bucketName = process.env.REVIEW_CENTER_BUCKET_NAME;
    }
    const params = {
      Bucket: bucketName,
      Key: objectKey,
    };

    try {
      await s3.deleteObject(params);
      console.log(
        `File ${objectKey} deleted successfully from bucket ${bucketName}.`,
      );

      const deleteDocumentParams = {
        TableName: process.env.STUDENT_DOCUMENTS,
        Key: {
          PK: email,
          SK: `${oapName}_${applicationId}_${type}_${documentId}`,
        },
      };
      await this.dynamoDBService.deleteObject(deleteDocumentParams);
    } catch (err) {
      console.error(
        `Error deleting file ${objectKey} from bucket ${bucketName} or from ${process.env.STUDENT_DOCUMENTS} table:`,
        err,
      );
      throw err;
    }
  }

  async publishMessageToSNS(
    message: string,
    topicArn: string,
    attributes: Record<string, any>,
    messageGroupId: string = null,
  ): Promise<any> {
    const params = {
      Message: message,
      MessageAttributes: attributes,
      TopicArn: topicArn,
    };
    if (messageGroupId) {
      params['MessageGroupId'] = messageGroupId;
    }
    try {
      return this.sns.publish(params).promise();
    } catch (error) {
      throw error;
    }
  }
  async buildPdfGenRequest(
    sections: any[],
    studentDetails: any,
    oapDetails: any,
  ): Promise<any> {
    try {
      const request = {
        student: studentDetails,
        sections: {
          content: [],
          address: [],
          images: {},
          currentDate: new Date(),
          ...oapDetails?.pdfInfo?.theme,
        },
      };
      request['brand'] = oapDetails?.pdfInfo?.brand;
      const sortedSections = sections.sort((a, b) => {
        const orderA = a.displayOrder || Number.MAX_SAFE_INTEGER;
        const orderB = b.displayOrder || Number.MAX_SAFE_INTEGER;
        return orderA - orderB;
      });

      request.sections.content.push(...sortedSections);

      if (oapDetails?.pdfInfo?.images) {
        for (const image of oapDetails.pdfInfo.images) {
          const base64Img = await this.s3Service.getTextFileFromS3(
            image.location,
            image.bucketName,
          );
          request.sections.images[image.key] = base64Img;
        }
      }
      if (studentDetails?.signature) {
        const signaturePath = studentDetails?.signature?.[0]?.path
          ? studentDetails?.signature?.[0]?.path
          : await this.getStudentsDocuments(
            studentDetails?.email,
            oapDetails.PK,
            'signature',
            studentDetails?.applicationId,
            studentDetails?.signature?.[0]?.documentId,
          )?.then((response) =>
            response && Object.keys(response).length > 0
              ? response.Item?.path
              : null,
          );
        if (signaturePath) {
          request.sections.images['signature'] =
            await `data:image/png;base64,${await this.s3Service.getObjectAsBase64ByRole(
              process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
              this.getBucketNameByBrand(studentDetails?.brand),
              signaturePath,
            )}`;
        }
      }

      request.sections.address.push(...oapDetails?.pdfInfo?.address);

      return request;
    } catch (error) {
      throw error;
    }
  }
  getCurrentDate(): { formattedDate: string } {
    const currentDate = new Date();
    const monthName = currentDate.toLocaleString('default', { month: 'long' });
    const year = currentDate.getFullYear();

    const formattedDate = `${monthName}, ${year}`;

    return { formattedDate };
  }

  async processOcrDocument(request: any): Promise<any> {
    try {
      const response = await this.externalService.postData(
        request,
        process.env.OCR_API_ENDPOINT,
      );

      if (!response) {
        throw new HttpException(
          'No response received from OCR service',
          HttpStatus.BAD_GATEWAY,
        );
      }

      return response;
    } catch (error) {
      console.error('Error processing OCR document:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          message: 'Failed to process document with OCR service',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async getOpportunityById(
    opportunityId: string,
    APIKEY: string,
    request: any,
  ): Promise<any> {
    try {
      const response = await this.salesforceService.fetchData(
        `gus/getOpportunitiesById/${opportunityId}`,
        APIKEY,
        request,
      );

      console.log(response);

      // Map the Salesforce fields to the required format
      if (response) {
        return {
          ...response[0],
          currentIntake: {
            label: this.formatDateByBrand(
              response[0].Product_Intake_Date__c,
              response[0].BusinessUnitFilter__c,
            ),
            value: response[0].Product_Intake_Date__c,
          },
          productId: response[0]?.OpportunityLineItems?.records[0]?.Product2Id,
          currentProgram: {
            label: response[0].ProgrammeName__c,
            value: response[0].Programme__c,
          },
        };
      }

      return response;
    } catch (error) {
      console.error(`Error fetching opportunity ${opportunityId}:`, error);
      throw error;
    }
  }

  formatDateByBrand(dateString: string, brand: string): string {
    if (!dateString) return '';

    const dateObj = new Date(dateString);

    switch (brand) {
      case 'IBAT':
        return new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit',
        }).format(dateObj);
      case 'HZU':
        return new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: '2-digit',
          year: 'numeric',
        }).format(dateObj);
      case 'UCW':
        return new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'long',
        }).format(dateObj);
      case 'UEG':
        return new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'long',
        }).format(dateObj);
      default:
        return new Intl.DateTimeFormat('en-US', {
          month: 'long',
          year: 'numeric',
        }).format(dateObj);
    }
  }
  async getSectionByFormType(
    oapName: string,
    formType: string,
    mode: string,
    language: string | null = null,
  ): Promise<any> {
    const params = {
      TableName: process.env.GUS_OAP_FORM_SECTION_TABLE,
      KeyConditionExpression: 'PK = :pkValue and begins_with(SK, :skValue)',
      ExpressionAttributeValues: {
        ':pkValue': `${oapName}_${mode}`,
        ':skValue': formType,
      },
    };

    const sectionDetails = await this.dynamoDBService.queryObjects(params);
    const items = sectionDetails?.Items || [];

    if (!language) return items;

    return Promise.all(
      items.map((item) =>
        this.oapTranslationService.translateJson(item, language),
      ),
    );
  }

  async submitChangeRequestForm(
    requestData,
    oap: string,
    mode: string,
    request,
  ): Promise<any> {
    const currentUTC = new Date().toISOString();
    try {
      // Log: Submit request initiated
      await this.log(
        requestData.opportunityId,
        request.requestId,
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Event.CHANGE_REQUEST_INITIATED,
        this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
        requestData,
        requestData,
        'Change request form submission initiated',
        oap,
        requestData.opportunityId,
        'Opportunity',
        requestData.opportunityId,
      );

      const upperMode = mode.toUpperCase();
      const upperFormType = requestData.formType.toUpperCase();
      const bucketName = this.getBucketNameByBrand(oap);

      try {
        const oapDetail = await this.getOapDetails(oap, upperMode);

        // Log: Get OAP details completed
        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Event.GET_OAP_DETAILS_COMPLETED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          requestData,
          oapDetail,
          'OAP details retrieved successfully',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
        );

        const formTypeDetails = oapDetail.Item.requestTypes.find(
          (type) => type.formName === upperFormType,
        );

        // Log: Change request object formed
        const changeRequestPayload = {
          Deferral_Reason__c: requestData.deferralReason,
          Description: requestData?.otherReason,
          Deferral_Product__c: requestData.productId,
          Opportunity__c: requestData.opportunityId,
          Requested_from__c:
            upperMode === 'AGENT' ? 'Agent portal' : 'Direct portal',
          Status: formTypeDetails.status,
          RecordTypeId: formTypeDetails.recordTypeId,
          Subject: formTypeDetails.subject,
        };

        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.CHANGE_REQUEST_OBJECT_FORMED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          requestData,
          changeRequestPayload,
          'Change request object formed',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
        );

        const documentId = uuidv4();
        const objectKey = `${requestData.opportunityId}/${formTypeDetails.documentType}/${documentId}.pdf`;

        const sections = await this.getSectionByFormType(
          oap,
          upperFormType,
          upperMode,
        );

        // Log: PDF request formed
        const pdfRequest = await this.buildPdfGenRequest(
          sections,
          requestData,
          oapDetail.Item,
        );
        // Log: PDF request initiated
        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.EVENT_INITIATED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          pdfRequest,
          { path: process.env.PDF_REQUEST_PATH },
          'PDF generation request initiated',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
          'OpportunityId',
        );

        const { pdfBase64 } = await this.externalService.postData(
          pdfRequest,
          process.env.PDF_REQUEST_PATH,
        );

        // Log: PDF request completed
        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.PDF_REQUEST_COMPLETED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          {},
          { success: true, pdfSize: pdfBase64.length, pdfBase64 },
          'PDF generation completed successfully',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
          'OpportunityId',
        );

        const signedUrl = await this.buildUploadFileSignedUrl(
          bucketName,
          objectKey,
          process.env.S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN,
          'application/pdf',
        );

        await this.externalService.putData(
          Buffer.from(pdfBase64, 'base64'),
          signedUrl,
          { 'Content-Type': 'application/pdf' },
        );

        // Log: Uploaded to S3
        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.S3_UPLOAD_COMPLETED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          { bucketName, objectKey },
          { success: true },
          'PDF uploaded to S3 successfully',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
          'OpportunityId',
        );

        const opportunityFilePayload = {
          DocumentType__c: formTypeDetails.documentType,
          Name: `${documentId}.pdf`,
          OriginalValue__c: `${documentId}.pdf`,
          FilePath__c: objectKey,
          Opportunity__c: requestData.opportunityId,
          S3FileName__c: objectKey,
          FullUrl__c: objectKey,
          BucketName__c: bucketName,
        };

        const compositeRequest = [
          {
            method: 'POST',
            url: `/services/data/v60.0/sobjects/ChangeRequest`,
            referenceId: 'changeRequest',
            body: changeRequestPayload,
          },
          {
            method: 'POST',
            url: `/services/data/v60.0/sobjects/OpportunityFile__c`,
            referenceId: 'opportunityFile',
            body: opportunityFilePayload,
          },
        ];

        // Log: SF composite request initiated
        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.SALESFORCE_REQUEST_INITIATED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          requestData,
          compositeRequest,
          'Salesforce composite request initiated',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
          'OpportunityId',
        );

        const response = await this.salesforceService.postData(
          'gus/composite',
          { compositeRequest },
          request.headers['x-api-key'],
        );

        // Log: SF composite request completed
        await this.log(
          requestData.opportunityId,
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.SALESFORCE_REQUEST_COMPLETED,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          compositeRequest,
          response,
          'Salesforce composite request completed successfully',
          oap,
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
          'OpportunityId',
        );

        return {
          success: true,
          changeRequestId: response.compositeResponse[0].body.id,
          opportunityFileId: response.compositeResponse[1].body.id,
        };
      } catch (error) {
        // Log: Get OAP details failed or other errors
        await this.cloudWatchLoggerService.error(
          request.requestId,
          currentUTC,
          this.loggerEnum.Component.OAP_BACKEND,
          this.loggerEnum.Component.OAP_FRONTEND,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Event.CHANGE_REQUEST_ERROR,
          this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
          requestData,
          { error: error.message, stack: error.stack },
          `Error in change request process: ${error.message}`,
          oap,
          requestData.opportunityId,
          `oap-backend/${requestData.opportunityId}/${request.requestId}`,
          'Opportunity',
          requestData.opportunityId,
          'Opportunity',
          requestData.opportunityId,
          'OpportunityId',
        );
        throw error;
      }
    } catch (error) {
      // Log: Any error in the overall process
      await this.cloudWatchLoggerService.error(
        request.requestId,
        currentUTC,
        this.loggerEnum.Component.OAP_BACKEND,
        this.loggerEnum.Component.OAP_FRONTEND,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Event.CHANGE_REQUEST_FAILED,
        this.loggerEnum.UseCase.CHANGE_REQUEST_FORM,
        requestData,
        { error: error.message, stack: error.stack },
        `Change request submission failed: ${error.message}`,
        oap,
        requestData.opportunityId,
        `oap-backend/${requestData.opportunityId}/${request.requestId}`,
        'Opportunity',
        requestData.opportunityId,
        'Opportunity',
        requestData.opportunityId,
        'OpportunityId',
      );
      console.error('Error submitting change request form:', error);
      throw new HttpException(
        'Failed to submit change request form',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get user migration configuration from database
   */
  async getUserMigrationConfig(
    oapName: string,
    mode: string,
  ): Promise<UserMigrationConfigDto> {
    try {
      const oapDetails = await this.dynamoDBService.getObject(
        process.env.GUS_OAP_TABLE,
        {
          PK: oapName,
          SK: mode,
        },
      );

      if (!oapDetails.Item) {
        throw new HttpException(
          `OAP configuration not found for ${oapName} with mode ${mode}`,
          HttpStatus.NOT_FOUND,
        );
      }

      const userMigrationConfig = oapDetails.Item.user_migration_config;
      if (!userMigrationConfig) {
        throw new HttpException(
          `User migration configuration not found for ${oapName}`,
          HttpStatus.NOT_FOUND,
        );
      }

      return userMigrationConfig;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to retrieve user migration configuration: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Process user migration from S3 file with batch processing
   */
  async processUserMigration(
    oapName: string,
    mode: string,
    batchSize: number = 15, // Default batch size to stay within AWS rate limits
  ): Promise<UserMigrationResponseDto> {
    try {
      console.log(`Starting user migration for ${oapName} with batch size: ${batchSize}`);

      // Get migration configuration
      const config = await this.getUserMigrationConfig(oapName, mode);
      console.log('Config:', config);

      // Retrieve file from S3
      const fileBuffer = await this.s3Service.getUserMigrationFile(
        config.s3_config.bucket,
        config.s3_config.file_name,
      );

      // Parse file
      const parsedUsers = await this.fileParserService.parseFile(
        fileBuffer,
        config.s3_config.file_type,
        config.column_mappings,
      );
      console.log('Parsed users:', parsedUsers);

      // Validate parsed data
      const { valid: validUsers, invalid: invalidUsers } =
        this.fileParserService.validateUserData(parsedUsers);

      if (invalidUsers.length > 0) {
        console.warn(`Found ${invalidUsers.length} invalid user records:`, invalidUsers);
      }

      console.log(`Processing ${validUsers.length} valid users in batches of ${batchSize}`);

      // Process valid users in batches
      const results = await this.processUserBatches(validUsers, config, oapName, batchSize);

      // Calculate statistics
      const successfulMigrations = results.filter(r => r.status === 'success').length;
      const failedMigrations = results.filter(r => r.status === 'failed').length;

      console.log(`Migration completed: ${successfulMigrations} successful, ${failedMigrations} failed`);

      return {
        totalUsers: validUsers.length,
        successfulMigrations,
        failedMigrations,
        results,
        configUsed: config,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `User migration failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Process users in batches with concurrent processing
   */
  private async processUserBatches(
    users: ParsedUserData[],
    config: UserMigrationConfigDto,
    oapName: string,
    batchSize: number,
  ): Promise<UserMigrationResultDto[]> {
    const allResults: UserMigrationResultDto[] = [];
    const totalBatches = Math.ceil(users.length / batchSize);

    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;

      console.log(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} users)`);

      try {
        // Process batch concurrently
        const batchResults = await this.processBatchConcurrently(batch, config, oapName);
        allResults.push(...batchResults);

        // Add a small delay between batches to respect AWS rate limits
        if (i + batchSize < users.length) {
          await this.delay(100); // 100ms delay between batches
        }
      } catch (error) {
        console.error(`Error processing batch ${batchNumber}:`, error);

        // Add failed results for the entire batch
        const failedResults = batch.map(user => ({
          email: user.email,
          status: 'failed' as const,
          error: `Batch processing failed: ${error.message}`,
        }));
        allResults.push(...failedResults);
      }
    }

    return allResults;
  }

  /**
   * Process a single batch of users concurrently with optimized database operations
   */
  private async processBatchConcurrently(
    batch: ParsedUserData[],
    config: UserMigrationConfigDto,
    oapName: string,
  ): Promise<UserMigrationResultDto[]> {
    // Create promises for Cognito user creation only (without database saves)
    const userPromises = batch.map(user =>
      this.createUserInCognitoOnly(user, config)
    );

    // Execute all promises concurrently and handle individual failures
    const results = await Promise.allSettled(userPromises);

    // Separate successful and failed results
    const successfulUsers: Array<{
      userData: ParsedUserData;
      oapName: string;
      cognitoUserId: string;
    }> = [];
    const finalResults: UserMigrationResultDto[] = [];

    results.forEach((result, index) => {
      const user = batch[index];

      if (result.status === 'fulfilled' && result.value.status === 'success') {
        successfulUsers.push({
          userData: user,
          oapName,
          cognitoUserId: result.value.cognitoUserId,
        });
        finalResults.push(result.value);
      } else {
        const error = result.status === 'rejected' ? result.reason : result.value.error;
        console.error(`Failed to process user ${user.email}:`, error);
        finalResults.push({
          email: user.email,
          status: 'failed' as const,
          error: error?.message || error || 'Unknown error occurred',
        });
      }
    });

    // Bulk save successful users to database
    if (successfulUsers.length > 0) {
      try {
        await this.saveUsersBatchToDatabase(successfulUsers);
        console.log(`Successfully saved ${successfulUsers.length} users to database`);
      } catch (error) {
        console.error('Failed to bulk save users to database:', error);

        // Mark all successful Cognito users as failed due to database save failure
        successfulUsers.forEach(({ userData }) => {
          const resultIndex = finalResults.findIndex(r => r.email === userData.email);
          if (resultIndex !== -1) {
            finalResults[resultIndex] = {
              email: userData.email,
              status: 'failed',
              error: 'Cognito user created but database save failed',
            };
          }
        });
      }
    }

    return finalResults;
  }

  /**
   * Create user in Cognito only (without database save) for batch processing
   */
  private async createUserInCognitoOnly(
    userData: ParsedUserData,
    config: UserMigrationConfigDto,
  ): Promise<UserMigrationResultDto> {
    try {
      // Initialize Cognito client
      const cognitoClient = new CognitoIdentityProviderClient({
        region: config.cognito_config.region,
      });

      // Prepare user attributes
      const userAttributes = [
        {
          Name: 'email',
          Value: userData.email,
        },
        {
          Name: 'email_verified',
          Value: 'true',
        },
      ];

      // Add optional attributes if present
      if (userData.name) {
        userAttributes.push({
          Name: 'name',
          Value: userData.name,
        });
      }

      if (userData.phone_number) {
        userAttributes.push({
          Name: 'phone_number',
          Value: userData.phone_number,
        });
      }

      // Create user in Cognito
      const createUserCommand = new AdminCreateUserCommand({
        UserPoolId: config.cognito_config.user_pool_id,
        Username: userData.email,
        UserAttributes: userAttributes,
        MessageAction: 'SUPPRESS', // Don't send welcome email
        TemporaryPassword: this.generateTemporaryPassword(),
      });

      const createUserResponse = await cognitoClient.send(createUserCommand);
      const cognitoUserId = createUserResponse.User?.Username;

      return {
        email: userData.email,
        status: 'success',
        cognitoUserId,
      };
    } catch (error) {
      console.error(`Error creating user ${userData.email} in Cognito:`, error);

      // Handle specific Cognito errors
      if (error.name === 'UsernameExistsException') {
        return {
          email: userData.email,
          status: 'failed',
          error: 'User already exists in Cognito',
        };
      }

      return {
        email: userData.email,
        status: 'failed',
        error: error.message || 'Unknown error occurred',
      };
    }
  }

  /**
   * Create user in Cognito with retry logic (for single user processing)
   */
  private async createUserInCognitoWithRetry(
    userData: ParsedUserData,
    config: UserMigrationConfigDto,
    oapName: string,
    maxRetries: number = 2,
  ): Promise<UserMigrationResultDto> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.createUserInCognitoOnly(userData, config);

        // If Cognito creation was successful, save to database
        if (result.status === 'success') {
          await this.saveUserToDatabase(userData, oapName, result.cognitoUserId);
        }

        return result;
      } catch (error) {
        lastError = error;

        // Don't retry for certain errors
        if (error.name === 'UsernameExistsException' ||
          error.name === 'InvalidParameterException') {
          break;
        }

        // Add exponential backoff for retries
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 100; // 200ms, 400ms, etc.
          await this.delay(delay);
          console.log(`Retrying user creation for ${userData.email} (attempt ${attempt + 1}/${maxRetries})`);
        }
      }
    }

    // All retries failed
    console.error(`All retry attempts failed for user ${userData.email}:`, lastError);
    return {
      email: userData.email,
      status: 'failed',
      error: lastError?.message || 'Failed after all retry attempts',
    };
  }

  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create user in Cognito and save to database
   */
  private async createUserInCognito(
    userData: ParsedUserData,
    config: UserMigrationConfigDto,
    oapName: string,
  ): Promise<UserMigrationResultDto> {
    try {
      // Initialize Cognito client
      const cognitoClient = new CognitoIdentityProviderClient({
        region: config.cognito_config.region,
      });

      // Prepare user attributes
      const userAttributes = [
        {
          Name: 'email',
          Value: userData.email,
        },
        {
          Name: 'email_verified',
          Value: 'true',
        },
        { Name: 'custom:firstName', Value: userData.firstName },
        { Name: 'custom:lastName', Value: userData.lastName },
        ...(userData.phoneNumber
          ? [{ Name: 'custom:phoneNumber', Value: userData.phoneNumber }]
          : []),
        ...(userData.country
          ? [{ Name: 'custom:country', Value: userData.country }]
          : []),
      ];

      // Create user in Cognito
      const createUserCommand = new AdminCreateUserCommand({
        UserPoolId: config.cognito_config.user_pool_id,
        Username: userData.email,
        UserAttributes: userAttributes,
        MessageAction: 'SUPPRESS', // Don't send welcome email
        TemporaryPassword: this.generateTemporaryPassword(),
      });

      const createUserResponse = await cognitoClient.send(createUserCommand);
      const cognitoUserId = createUserResponse.User?.Username;

      // Save user details to database
      await this.saveUserToDatabase(userData, oapName, cognitoUserId);

      return {
        email: userData.email,
        status: 'success',
        cognitoUserId,
      };
    } catch (error) {
      console.error(`Error creating user ${userData.email} in Cognito:`, error);

      // Handle specific Cognito errors
      if (error.name === 'UsernameExistsException') {
        return {
          email: userData.email,
          status: 'failed',
          error: 'User already exists in Cognito',
        };
      }

      return {
        email: userData.email,
        status: 'failed',
        error: error.message || 'Unknown error occurred',
      };
    }
  }

  /**
   * Save user details to database
   */
  private async saveUserToDatabase(
    userData: ParsedUserData,
    oapName: string,
    cognitoUserId?: string,
  ): Promise<void> {
    try {

      const userRecord = {
        PK: userData.email,
        SK: oapName,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        passwordChangeRequired: true,
        isConfirmed: true,
        createdAt: new Date().toISOString(),
        userId: cognitoUserId, // Using email as userId for now
        ...(userData.phoneNumber && {
          phoneNumber: {
            countryCode: userData.phoneNumber.split('+')[1]?.substring(0, 2).toLowerCase() || 'in',
            dialCode: userData.phoneNumber.split('+')[1]?.substring(0, 2) || '91',
            number: userData.phoneNumber.split('+')[1]?.substring(2) || userData.phoneNumber,
            numberWithCode: userData.phoneNumber.startsWith('+') ? userData.phoneNumber : `+${userData.phoneNumber}`
          }
        }),
        ...(userData.country && {
          country: {
            label: userData.country,
            value: await this.getCountryCode(userData.country)
          }
        }),
      }

      await this.dynamoDBService.putObject(
        process.env.GUS_OAP_STUDENT_DETAILS_TABLE,
        {
          Item: userRecord,
        },
      );
    } catch (error) {
      console.error(`Error saving user ${userData.email} to database:`, error);
      throw new Error(`Failed to save user to database: ${error.message}`);
    }
  }

  /**
   * Save multiple users to database in batch (optimized for bulk operations)
   */
  private async saveUsersBatchToDatabase(
    userDataList: Array<{
      userData: ParsedUserData;
      oapName: string;
      cognitoUserId?: string;
    }>,
  ): Promise<void> {
    try {
      const batchWriteRequests = await Promise.all(
        userDataList.map(async ({ userData, oapName, cognitoUserId }) => ({
          PutRequest: {
            Item: {
              PK: userData.email,
              SK: oapName,
              firstName: userData.firstName,
              lastName: userData.lastName,
              email: userData.email,
              passwordChangeRequired: true,
              isConfirmed: true,
              createdAt: new Date().toISOString(),
              userId: cognitoUserId,
              ...(userData.phoneNumber && {
                phoneNumber: {
                  countryCode: userData.phoneNumber.split('+')[1]?.substring(0, 2).toLowerCase() || 'in',
                  dialCode: userData.phoneNumber.split('+')[1]?.substring(0, 2) || '91',
                  number: userData.phoneNumber.split('+')[1]?.substring(2) || userData.phoneNumber,
                  numberWithCode: userData.phoneNumber.startsWith('+') ? userData.phoneNumber : `+${userData.phoneNumber}`
                }
              }),
              ...(userData.country && {
                country: {
                  label: userData.country,
                  value: await this.getCountryCode(userData.country)
                }
              }),
            },
          },
        }))
      );

      // DynamoDB batch write can handle up to 25 items at a time
      const batchSize = 25;
      for (let i = 0; i < batchWriteRequests.length; i += batchSize) {
        const batch = batchWriteRequests.slice(i, i + batchSize);

        const params = {
          RequestItems: {
            [process.env.GUS_OAP_STUDENT_DETAILS_TABLE]: batch,
          },
        };

        await this.dynamoDBService.batchWriteObjects(params);
      }
    } catch (error) {
      console.error('Error saving users batch to database:', error);
      throw new Error(`Failed to save users batch to database: ${error.message}`);
    }
  }

  /**
   * Generate a temporary password for new users
   */
  private generateTemporaryPassword(): string {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    // Ensure password has at least one of each required character type
    password += 'A'; // uppercase
    password += 'a'; // lowercase
    password += '1'; // number
    password += '!'; // special character

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * Process application migration from S3 file with batch processing
   */
  async processApplicationMigration(
    oapName: string,
    mode: string,
    batchSize: number = 10,
    dryRun: boolean = false,
  ): Promise<any> {
    const startTime = Date.now();
    const migrationId = `${oapName}_${mode}_${Date.now()}`;

    try {
      console.log(`Starting application migration for ${oapName} with batch size: ${batchSize}, dryRun: ${dryRun}`);

      // Get migration configuration
      const config = await this.getApplicationMigrationConfig(oapName, mode);

      // Retrieve applications from S3 folder
      const applications = await this.getApplicationsFromS3Folder(
        config.s3_config.bucket,
        config.s3_config.file_path,
      );

      if (!Array.isArray(applications) || applications.length === 0) {
        throw new Error(`No applications found in S3 path: ${config.s3_config.file_path}`);
      }

      console.log(`Found ${applications.length} applications to migrate`);

      // Initialize result storage
      const resultStorage = await this.initializeMigrationResultStorage(migrationId, oapName, applications.length);

      // Process applications in batches with efficient storage
      const allResults = await this.processApplicationBatchesWithStorage(
        applications,
        config,
        oapName,
        batchSize,
        dryRun,
        resultStorage,
      );

      const successfulMigrations = allResults.filter(r => r.status === 'success').length;
      const failedMigrations = allResults.filter(r => r.status === 'failed').length;
      const skippedApplications = allResults.filter(r => r.status === 'skipped').length;

      const processingTimeMs = Date.now() - startTime;
      const documentsProcessed = allResults.reduce((sum, r) => sum + (r.documentsSaved || 0), 0);
      const picklistFieldsProcessed = allResults.reduce((sum, r) => sum + (r.picklistFieldsMapped || 0), 0);

      console.log(`Application migration completed: ${successfulMigrations} successful, ${failedMigrations} failed, ${skippedApplications} skipped`);

      return {
        migrationId,
        totalApplications: applications.length,
        successfulMigrations,
        failedMigrations,
        skippedApplications,
        processingTimeMs,
        documentsProcessed,
        picklistFieldsProcessed,
        resultFiles: {
          successfulApplications: `s3://${resultStorage.bucketName}/${resultStorage.successFilePath}`,
          failedApplications: `s3://${resultStorage.bucketName}/${resultStorage.failedFilePath}`,
          migrationSummary: `s3://${resultStorage.bucketName}/${resultStorage.summaryFilePath}`,
        },
        message: `Migration completed. Results stored in S3. Check the summary file for detailed information.`,
      };
    } catch (error) {
      console.error('Application migration failed:', error);
      throw new HttpException(
        `Application migration failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get application migration configuration by combining S3 config from OAP table and brand mappings from files
   */
  private async getApplicationMigrationConfig(
    oapName: string,
    mode: string,
  ): Promise<ApplicationMigrationConfigDto> {
    try {
      // Get OAP details which contains S3 configuration
      const oapDetails = await this.dynamoDBService.getObject(
        process.env.GUS_OAP_TABLE,
        {
          PK: oapName,
          SK: mode,
        },
      );

      if (!oapDetails.Item) {
        throw new Error(`No OAP configuration found for ${oapName}_${mode}`);
      }

      // Extract brand from OAP details
      const brand = oapDetails.Item.brand || oapDetails.Item.brandCode;
      if (!brand) {
        throw new Error(`No brand information found in OAP configuration for ${oapName}_${mode}`);
      }

      // Get S3 configuration from OAP details
      const s3Config = {
        bucket: oapDetails.Item.migrationS3Bucket || oapDetails.Item.s3Bucket || 'campusnet-sf-sync-stage',
        file_path: oapDetails.Item.migrationS3Path || `legacy-applications/${oapName.toLowerCase()}`,
      };

      // Load brand-specific mappings from project files
      const brandMappings = getBrandMappings(brand);

      // Combine into final configuration
      const config: ApplicationMigrationConfigDto = {
        s3_config: s3Config,
        brand: brand,
        field_mappings: {
          directFields: brandMappings.directFields,
          documentFields: brandMappings.documentFields,
          arrayFields: brandMappings.arrayFields,
        },
        picklist_mappings: brandMappings.picklistMappings,
        transformation_rules: brandMappings.transformationRules,
      };

      // Add subsection mappings to the configuration
      (config as any).subsection_mappings = brandMappings.subsectionMappings;

      console.log(`Loaded migration config for brand: ${brand}, S3 path: ${s3Config.file_path}`);
      return config;
    } catch (error) {
      console.error('Error building application migration config:', error);
      throw error;
    }
  }

  /**
   * Initialize migration result storage for efficient handling of large datasets
   */
  private async initializeMigrationResultStorage(migrationId: string, oapName: string, totalApplications: number) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const bucketName = 'app-hero-brand-logos';

    const storage = {
      migrationId,
      oapName,
      timestamp,
      bucketName,
      successFilePath: `migration-results/${oapName}/${migrationId}/successful-applications-${timestamp}.jsonl`,
      failedFilePath: `migration-results/${oapName}/${migrationId}/failed-applications-${timestamp}.jsonl`,
      summaryFilePath: `migration-results/${oapName}/${migrationId}/migration-summary-${timestamp}.json`,
      totalApplications,
      processedCount: 0,
      successCount: 0,
      failedCount: 0,
      skippedCount: 0,
    };

    console.log(`Initialized migration result storage:`, {
      migrationId,
      successFilePath: storage.successFilePath,
      failedFilePath: storage.failedFilePath,
      summaryFilePath: storage.summaryFilePath,
    });

    return storage;
  }

  /**
   * Process applications in batches with efficient storage for large datasets
   */
  private async processApplicationBatchesWithStorage(
    applications: any[],
    config: ApplicationMigrationConfigDto,
    oapName: string,
    batchSize: number,
    dryRun: boolean,
    resultStorage: any,
  ): Promise<ApplicationMigrationResultDto[]> {
    const allResults: ApplicationMigrationResultDto[] = [];
    const totalBatches = Math.ceil(applications.length / batchSize);

    for (let i = 0; i < applications.length; i += batchSize) {
      const batch = applications.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;

      console.log(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} applications)`);

      try {
        const batchResults = await this.processBatchApplications(
          batch,
          config,
          oapName,
          dryRun,
        );

        // Store results efficiently
        await this.storeBatchResults(batchResults, resultStorage);

        allResults.push(...batchResults);

        // Update progress
        resultStorage.processedCount += batch.length;
        console.log(`Progress: ${resultStorage.processedCount}/${resultStorage.totalApplications} applications processed`);

        // Add delay between batches to respect rate limits
        if (i + batchSize < applications.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Batch ${batchNumber} failed:`, error);

        // Add failed results for all applications in this batch
        const failedResults: ApplicationMigrationResultDto[] = batch.map(app => ({
          applicationId: app.applicationId || 'unknown',
          email: app.email || 'unknown',
          status: 'failed',
          error: `Batch processing failed: ${error.message}`,
        }));

        await this.storeBatchResults(failedResults, resultStorage);
        allResults.push(...failedResults);
        resultStorage.processedCount += batch.length;
      }
    }

    // Finalize storage with summary
    await this.finalizeMigrationStorage(resultStorage, allResults);

    return allResults;
  }

  /**
   * Store batch results efficiently to S3 in JSONL format for large datasets
   */
  private async storeBatchResults(results: ApplicationMigrationResultDto[], resultStorage: any): Promise<void> {
    const successfulResults = results.filter(r => r.status === 'success');
    const failedResults = results.filter(r => r.status === 'failed');
    const skippedResults = results.filter(r => r.status === 'skipped');

    // Update counters
    resultStorage.successCount += successfulResults.length;
    resultStorage.failedCount += failedResults.length;
    resultStorage.skippedCount += skippedResults.length;

    try {
      // Store successful applications (append to JSONL file)
      if (successfulResults.length > 0) {
        const successfulData = successfulResults.map(result => {
          // Remove large transformedData from storage to save space, keep only summary
          const { transformedData, ...resultSummary } = result;
          return {
            ...resultSummary,
            hasTransformedData: !!transformedData,
            transformedDataSize: transformedData ? JSON.stringify(transformedData).length : 0,
          };
        }).map(r => JSON.stringify(r)).join('\n') + '\n';

        await this.appendToS3File(resultStorage.bucketName, resultStorage.successFilePath, successfulData);
      }

      // Store failed applications (append to JSONL file)
      if (failedResults.length > 0) {
        const failedData = failedResults.map(r => JSON.stringify(r)).join('\n') + '\n';
        await this.appendToS3File(resultStorage.bucketName, resultStorage.failedFilePath, failedData);
      }

      // Store skipped applications with successful ones but mark them
      if (skippedResults.length > 0) {
        const skippedData = skippedResults.map(r => JSON.stringify(r)).join('\n') + '\n';
        await this.appendToS3File(resultStorage.bucketName, resultStorage.successFilePath, skippedData);
      }

    } catch (error) {
      console.error('Error storing batch results:', error);
      // Don't throw error to avoid stopping the migration process
    }
  }

  /**
   * Finalize migration storage with summary and metadata
   */
  private async finalizeMigrationStorage(resultStorage: any, allResults: ApplicationMigrationResultDto[]): Promise<void> {
    try {
      const summary = {
        migrationId: resultStorage.migrationId,
        oapName: resultStorage.oapName,
        timestamp: resultStorage.timestamp,
        totalApplications: resultStorage.totalApplications,
        processedApplications: resultStorage.processedCount,
        successfulMigrations: resultStorage.successCount,
        failedMigrations: resultStorage.failedCount,
        skippedApplications: resultStorage.skippedCount,
        successRate: ((resultStorage.successCount / resultStorage.totalApplications) * 100).toFixed(2) + '%',
        files: {
          successfulApplications: resultStorage.successFilePath,
          failedApplications: resultStorage.failedFilePath,
          summary: resultStorage.summaryFilePath,
        },
        processingStats: {
          totalDocumentsProcessed: allResults.reduce((sum, r) => sum + (r.documentsSaved || 0), 0),
          totalPicklistFieldsProcessed: allResults.reduce((sum, r) => sum + (r.picklistFieldsMapped || 0), 0),
        },
        completedAt: new Date().toISOString(),
      };

      // Store summary
      await this.uploadToS3(
        resultStorage.bucketName,
        resultStorage.summaryFilePath,
        JSON.stringify(summary, null, 2),
        'application/json'
      );

      console.log(`Migration completed. Summary stored at: s3://${resultStorage.bucketName}/${resultStorage.summaryFilePath}`);
      console.log(`Successful applications: s3://${resultStorage.bucketName}/${resultStorage.successFilePath}`);
      console.log(`Failed applications: s3://${resultStorage.bucketName}/${resultStorage.failedFilePath}`);

    } catch (error) {
      console.error('Error finalizing migration storage:', error);
    }
  }

  /**
   * Append data to S3 file (simulated by reading, appending, and re-uploading)
   */
  private async appendToS3File(bucketName: string, filePath: string, data: string): Promise<void> {
    try {
      let existingData = '';

      // Try to read existing file
      try {
        existingData = await this.s3Service.getTextFileFromS3(filePath, bucketName);
      } catch (error) {
        // File doesn't exist yet, which is fine for first write
        console.log(`Creating new file: ${filePath}`);
      }

      // Append new data
      const updatedData = existingData + data;

      // Upload updated file
      await this.uploadToS3(bucketName, filePath, updatedData, 'application/x-ndjson');

    } catch (error) {
      console.error(`Error appending to S3 file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Upload data to S3
   */
  private async uploadToS3(bucketName: string, key: string, data: string, contentType: string): Promise<void> {
    try {
      const params = {
        Bucket: bucketName,
        Key: key,
        Body: data,
        ContentType: contentType,
      };

      await this.s3Service.uploadObject(params);
    } catch (error) {
      console.error(`Error uploading to S3: ${key}`, error);
      throw error;
    }
  }

  /**
   * Process applications in batches with concurrent processing
   */
  private async processApplicationBatches(
    applications: any[],
    config: ApplicationMigrationConfigDto,
    oapName: string,
    batchSize: number,
    dryRun: boolean,
  ): Promise<ApplicationMigrationResultDto[]> {
    const allResults: ApplicationMigrationResultDto[] = [];
    const totalBatches = Math.ceil(applications.length / batchSize);

    for (let i = 0; i < applications.length; i += batchSize) {
      const batch = applications.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;

      console.log(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} applications)`);

      try {
        const batchResults = await this.processBatchApplications(
          batch,
          config,
          oapName,
          dryRun,
        );
        allResults.push(...batchResults);

        // Add delay between batches to respect rate limits
        if (i + batchSize < applications.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Batch ${batchNumber} failed:`, error);

        // Add failed results for all applications in this batch
        const failedResults: ApplicationMigrationResultDto[] = batch.map(app => ({
          applicationId: app.applicationId || 'unknown',
          email: app.email || 'unknown',
          status: 'failed',
          error: `Batch processing failed: ${error.message}`,
        }));

        allResults.push(...failedResults);
      }
    }

    return allResults;
  }

  /**
   * Process a single batch of applications concurrently
   */
  private async processBatchApplications(
    batch: any[],
    config: ApplicationMigrationConfigDto,
    oapName: string,
    dryRun: boolean,
  ): Promise<ApplicationMigrationResultDto[]> {
    // Process applications concurrently within the batch
    const applicationPromises = batch.map(application =>
      this.processSingleApplication(application, config, oapName, dryRun)
    );

    // Execute all promises and handle individual failures
    const results = await Promise.allSettled(applicationPromises);

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          applicationId: batch[index].applicationId || 'unknown',
          email: batch[index].email || 'unknown',
          status: 'failed',
          error: result.reason?.message || 'Unknown error during processing',
        };
      }
    });
  }

  /**
   * Process a single application through the complete migration pipeline
   */
  private async processSingleApplication(
    legacyApplication: any,
    config: ApplicationMigrationConfigDto,
    oapName: string,
    dryRun: boolean,
  ): Promise<ApplicationMigrationResultDto> {
    try {
      const applicationId = legacyApplication.uuid;
      const email = legacyApplication.studentMainData.email;

      if (!applicationId || !email) {
        return {
          applicationId: applicationId || 'unknown',
          email: email || 'unknown',
          status: 'failed',
          error: 'Missing required fields: applicationId or email',
        };
      }

      console.log(`Processing application ${applicationId} for ${email}`);

      // Step 1: Transform legacy application data to new format
      const transformedData = await this.transformApplicationData(
        legacyApplication,
        config.field_mappings,
        config.transformation_rules,
        (config as any).subsection_mappings,
      );

      // Step 2: Extract and save documents to separate database
      const documentResults = await this.saveApplicationDocuments(
        legacyApplication,
        applicationId,
        email,
        dryRun,
        { documentFields: config.field_mappings.documentFields },
      );

      // Step 3: Add document references to transformed data
      this.addDocumentReferencesToTransformedData(transformedData, documentResults);

      // Step 4: Map picklist fields
      const picklistResults = await this.mapPicklistFields(
        transformedData,
        config.picklist_mappings,
        oapName,
      );

      // Step 5: Save transformed application to student application database
      if (!dryRun) {
        await this.saveStudentApplication(
          transformedData,
          oapName,
          applicationId,
          email,
        );
      }

      // const documentsSaved = documentResults.filter(d => d.status === 'success').length;
      const picklistFieldsMapped = picklistResults.filter(p => p.status === 'success').length;

      return {
        applicationId,
        email,
        status: 'success',
        // documentsSaved,
        picklistFieldsMapped,
        transformedData: dryRun ? transformedData : undefined,
      };
    } catch (error) {
      console.error(`Error processing application:`, error);
      return {
        applicationId: legacyApplication.applicationId || 'unknown',
        email: legacyApplication.email || 'unknown',
        status: 'failed',
        error: error.message,
      };
    }
  }

  /**
   * Transform legacy application data to new format using field mappings
   */
  private async transformApplicationData(
    legacyData: any,
    fieldMappings: any,
    transformationRules?: Record<string, any>,
    subsectionMappings?: Record<string, Record<string, string>>,
  ): Promise<any> {
    const transformedData: any = {};

    // Apply direct field mappings
    if (fieldMappings.directFields) {
      for (const [legacyField, newFieldConfig] of Object.entries(fieldMappings.directFields)) {
        const value = this.getNestedProperty(legacyData, legacyField);
        if (value !== undefined && value !== null) {
          // Handle both string and array mappings
          if (Array.isArray(newFieldConfig)) {
            // Multiple fields mapped from single source
            for (const newField of newFieldConfig) {
              this.setNestedProperty(transformedData, newField, value);
            }
          } else {
            // Single field mapping
            this.setNestedProperty(transformedData, newFieldConfig as string, value);
          }
        }
      }
    }

    // Handle array fields (educationSchools, eqhe)
    if (fieldMappings.arrayFields) {
      for (const [legacyField, newFieldConfig] of Object.entries(fieldMappings.arrayFields)) {
        const arrayData = this.getNestedProperty(legacyData, legacyField);
        if (Array.isArray(arrayData)) {
          // Handle both string and array mappings for array fields
          if (Array.isArray(newFieldConfig)) {
            // Multiple fields mapped from single source array
            for (const newField of newFieldConfig) {
              transformedData[newField] = arrayData;
            }
          } else {
            // Single field mapping
            transformedData[newFieldConfig as string] = arrayData;
          }
        }
      }
    }

    // Apply transformation rules if provided
    if (transformationRules) {
      for (const [field, rule] of Object.entries(transformationRules)) {
        const currentValue = this.getNestedProperty(transformedData, field);

        if (rule.type === 'date_format' && currentValue) {
          const transformedDate = this.transformDateFormat(
            currentValue,
            rule.from_format,
            rule.to_format,
          );
          this.setNestedProperty(transformedData, field, transformedDate);
        } else if (rule.type === 'boolean_conversion' && currentValue !== undefined) {
          const boolValue = this.transformBooleanValue(currentValue, rule);
          this.setNestedProperty(transformedData, field, boolValue);
        } else if (rule.type === 'concatenate' && rule.fields) {
          const values = rule.fields.map(f => this.getNestedProperty(legacyData, f)).filter(v => v);
          this.setNestedProperty(transformedData, field, values.join(rule.separator || ' '));
        } else if (rule.type === 'default_value' && !currentValue) {
          this.setNestedProperty(transformedData, field, rule.value);
        } else if (rule.type === 'type_conversion' && currentValue !== undefined) {
          const convertedValue = this.convertFieldType(currentValue, rule);
          this.setNestedProperty(transformedData, field, convertedValue);
        }
      }
    }

    // Add metadata
    transformedData.migrationMetadata = {
      migratedAt: new Date().toISOString(),
      sourceSystem: 'legacy_s3',
      migrationVersion: '1.0',
      originalId: legacyData._id,
      originalUuid: legacyData.uuid,
    };

    // Apply subsection mappings for array fields
    if (subsectionMappings) {
      this.applySubsectionMappings(transformedData, subsectionMappings, transformationRules);
    }

    // Apply country code transformations
    const countryTransformedData = await transformCountryFields(
      transformedData,
      (countryName: string) => this.getCountryCode(countryName)
    );

    // Apply phone number formatting
    const phoneTransformedData = transformPhoneNumberFields(countryTransformedData);

    // Apply date transformations to all date fields
    this.applyDateTransformationsToObject(phoneTransformedData);

    return phoneTransformedData;
  }

  /**
   * Retrieve applications from S3 folder (can contain multiple JSON files)
   */
  private async getApplicationsFromS3Folder(
    bucket: string,
    folderPath: string,
  ): Promise<any[]> {
    try {
      // For now, assume there's a single JSON file in the folder
      // This can be enhanced to list all JSON files in the folder and merge them
      const fileName = `${folderPath}/applications.json`;

      console.log(`Retrieving applications from S3: ${bucket}/${fileName}`);

      const fileContent = await this.s3Service.getTextFileFromS3(
        fileName,
        bucket,
      );

      const applications = JSON.parse(fileContent);

      if (!Array.isArray(applications)) {
        // If it's a single application object, wrap it in an array
        return [applications];
      }

      return applications;
    } catch (error) {
      console.error(`Error retrieving applications from S3 folder ${folderPath}:`, error);
      throw new Error(`Failed to retrieve applications from S3: ${error.message}`);
    }
  }

  /**
   * Format date string to YYYY-MM-DD format
   */
  private formatDateToYYYYMMDD(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date format: ${dateString}`);
        return dateString; // Return original if invalid
      }

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn(`Error formatting date ${dateString}:`, error);
      return dateString; // Return original if error
    }
  }

  /**
   * Apply date transformations to all date fields in an object
   */
  private applyDateTransformationsToObject(obj: any): void {
    const dateFields = [
      'birthDate', 'dateOfBirth', 'startDate', 'endDate', 'graduationDate',
      'issueDate', 'expiryDate', 'submittedAt', 'createdAt', 'updatedAt',
      'completionDate', 'enrollmentDate', 'registrationDate'
    ];

    for (const field of dateFields) {
      if (obj[field] && typeof obj[field] === 'string') {
        obj[field] = this.formatDateToYYYYMMDD(obj[field]);
      }
    }
  }

  /**
   * Apply transformation rules to an object (for subsection array items)
   */
  private applyTransformationRulesToObject(obj: any, transformationRules?: Record<string, any>): void {
    if (!transformationRules) {
      // Fallback to the old method if no transformation rules available
      this.applyDateTransformationsToObject(obj);
      return;
    }

    // Apply transformation rules for each field in the object
    for (const [field, rule] of Object.entries(transformationRules)) {
      const currentValue = obj[field];

      if (rule.type === 'date_format' && currentValue) {
        const transformedDate = this.transformDateFormat(
          currentValue,
          rule.from_format,
          rule.to_format,
        );
        obj[field] = transformedDate;
      } else if (rule.type === 'boolean_conversion' && currentValue !== undefined) {
        const convertedValue = this.transformBooleanValue(currentValue, rule);
        obj[field] = convertedValue;
      }
    }
  }

  /**
   * Apply subsection mappings to array fields
   */
  private applySubsectionMappings(
    transformedData: any,
    subsectionMappings: Record<string, Record<string, string>>,
    transformationRules?: Record<string, any>,
  ): void {
    for (const [arrayFieldName, mappings] of Object.entries(subsectionMappings)) {
      const arrayData = transformedData[arrayFieldName];

      if (Array.isArray(arrayData)) {
        transformedData[arrayFieldName] = arrayData.map(item => {
          const mappedItem: any = {};

          for (const [oldField, newFieldConfig] of Object.entries(mappings)) {
            if (item.hasOwnProperty(oldField)) {
              // Handle both string and array mappings for subsection fields
              if (Array.isArray(newFieldConfig)) {
                // Multiple fields mapped from single source
                for (const newField of newFieldConfig) {
                  mappedItem[newField] = item[oldField];
                }
              } else {
                // Single field mapping
                mappedItem[newFieldConfig] = item[oldField];
              }
            }
          }

          // Apply date transformations to mapped items only
          this.applyTransformationRulesToObject(mappedItem, transformationRules);

          return mappedItem;
        });
      }
    }
  }


  /**
   * Save application documents to separate database
   */
  private async saveApplicationDocuments(
    legacyApplication: any,
    applicationId: string,
    email: string,
    dryRun: boolean,
    brandMappings?: any,
  ): Promise<DocumentStorageResultDto[]> {
    const results: DocumentStorageResultDto[] = [];
    const getContentTypeAndFormat = (fileName: string): any => {
      const extension = fileName.split('.').pop()?.toLowerCase();
      const mimeTypes: { [key: string]: string } = {
        "pdf": "application/pdf",
        "doc": "application/msword",
        "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "xls": "application/vnd.ms-excel",
        "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "ppt": "application/vnd.ms-powerpoint",
        "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "txt": "text/plain",
        "csv": "text/csv",
        "rtf": "application/rtf",
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "gif": "image/gif",
        "bmp": "image/bmp",
        "tiff": "image/tiff",
        "tif": "image/tiff",
        "webp": "image/webp",
        "svg": "image/svg+xml",
        "mp3": "audio/mpeg",
        "wav": "audio/wav",
        "ogg": "audio/ogg",
        "aac": "audio/aac",
        "flac": "audio/flac",
        "m4a": "audio/mp4",
        "mp4": "video/mp4",
        "mov": "video/quicktime",
        "avi": "video/x-msvideo",
        "wmv": "video/x-ms-wmv",
        "flv": "video/x-flv",
        "mkv": "video/x-matroska",
        "webm": "video/webm",
        "zip": "application/zip",
        "rar": "application/vnd.rar",
        "tar": "application/x-tar",
        "gz": "application/gzip",
        "7z": "application/x-7z-compressed",
        "html": "text/html",
        "htm": "text/html",
        "css": "text/css",
        "js": "application/javascript",
        "json": "application/json",
        "xml": "application/xml",
        "yaml": "text/yaml",
        "yml": "text/yaml",
        "woff": "font/woff",
        "woff2": "font/woff2",
        "ttf": "font/ttf",
        "otf": "font/otf",
        "apk": "application/vnd.android.package-archive",
        "exe": "application/octet-stream",
        "dmg": "application/octet-stream",
        "iso": "application/x-iso9660-image"
      };

      return {
        contentType: extension && mimeTypes[extension] ? mimeTypes[extension] : "application/octet-stream",
        documentFormat: extension || 'unknown',
      };
    };

    // Extract documents from the legacy data structure
    const documentFields = [
      'fileCV', 'photo', 'educationSchoolsCertificateFile', 'educationSchoolsTranscriptFile',
      'fileGermanTest', 'fileEnglishTest', 'fileEntranceTest', 'filePortfolio',
      'applyReasonFile', 'nonObjectionCertificate', 'schoolLeavingCertificate',
      'otherFiles', 'fileEducationQualification'
    ];

    for (const fieldName of documentFields) {
      const documents = this.getNestedProperty(legacyApplication, `data.${fieldName}`);

      if (Array.isArray(documents)) {
        for (const document of documents) {
          try {
            const documentId = uuidv4();
            const documentType = this.mapDocumentType(fieldName, brandMappings);

            if (!dryRun) {
              // Save to documents database
              await this.dynamoDBService.putObject(
                process.env.STUDENT_DOCUMENTS || 'gus-student-documents',
                {
                  Item: {
                    PK: email,
                    SK: `${applicationId}_${documentType}_${documentId}`,
                    applicationId,
                    documentId,
                    documentType,
                    documentName: document.originalFileName,
                    ...getContentTypeAndFormat(document.originalFileName),
                    path: document.filePath,
                    s3BucketName: legacyApplication.pdfInfo?.s3BucketName || process.env.CAMPUSNET_BUCKET_NAME,
                    uploadedAt: new Date().toISOString(),
                    migrationMetadata: {
                      migratedAt: new Date().toISOString(),
                      sourceSystem: 'legacy_s3',
                      originalFieldName: fieldName,
                    },
                    ...document,
                  },
                },
              );
            }

            results.push({
              documentId,
              applicationId,
              documentType,
              key: brandMappings?.documentFields[`data.${fieldName}`],
              status: 'success',
              storageLocation: `${email}/${applicationId}_${documentType}_${documentId}`,
            });
          } catch (error) {
            results.push({
              documentId: 'unknown',
              applicationId,
              documentType: this.mapDocumentType(fieldName, brandMappings),
              status: 'failed',
              error: error.message,
            });
          }
        }
      }
    }

    return results;
  }

  /**
   * Map legacy document field names to document types using brand-specific mappings
   */
  private mapDocumentType(fieldName: string, brandMappings?: any): string {
    // Try to use brand-specific document field mappings first
    if (brandMappings?.documentFields) {
      const documentFieldKey = `data.${fieldName}`;
      const mappedType = brandMappings.documentFields[documentFieldKey];
      if (mappedType) {
        // Handle both string and array mappings, use the first value if array
        return Array.isArray(mappedType) ? mappedType[0] : mappedType;
      }
    }

    // Fallback to hardcoded mapping for backward compatibility
    const documentTypeMap: Record<string, string> = {
      'fileCV': 'CV',
      'photo': 'Photograph',
      'educationSchoolsCertificateFile': 'Higher Education Entrance Qualification',
      'educationSchoolsTranscriptFile': 'Final Higher Education Entrance Qualification',
      'fileGermanTest': 'Language certificate (German)',
      'fileEnglishTest': 'English Language Evidence',
      'fileEntranceTest': 'Entrance Test',
      'filePortfolio': 'Portfolio',
      'applyReasonFile': 'Letter of Motivation',
      'nonObjectionCertificate': 'No objection certificate',
      'schoolLeavingCertificate': 'De-registration certificate',
      'otherFiles': 'Other Document',
      'fileEducationQualification': 'Degree transcripts',
    };

    return documentTypeMap[fieldName] || fieldName;
  }

  /**
   * Add document references to transformed application data
   */
  private addDocumentReferencesToTransformedData(
    transformedData: any,
    documentResults: DocumentStorageResultDto[],
  ): void {
    // Group documents by type
    const documentsByType: Record<string, string[]> = {};

    for (const docResult of documentResults) {
      if (docResult.status === 'success') {
        if (!documentsByType[docResult.key]) {
          documentsByType[docResult.key] = [];
        }
        documentsByType[docResult.key].push(docResult.documentId);
      }
    }

    // Add document references to transformed data in the required format
    for (const [key, documentIds] of Object.entries(documentsByType)) {
      transformedData[key] = documentIds.map(documentId => ({
        documentId: documentId
      }));
    }
  }

  /**
   * Map picklist field values using existing lookup infrastructure
   */
  private async mapPicklistFields(
    applicationData: any,
    picklistMappings: Record<string, Record<string, string>>,
    oapName: string,
  ): Promise<PicklistMappingResultDto[]> {
    const results: PicklistMappingResultDto[] = [];

    for (const [fieldName, mappings] of Object.entries(picklistMappings)) {
      const fieldValue = this.getNestedProperty(applicationData, fieldName);

      if (fieldValue) {
        try {
          let mappedValue = fieldValue;

          // Check if there's a direct mapping
          if (mappings[fieldValue]) {
            mappedValue = mappings[fieldValue];
          } else {
            // Try to find mapping in picklist data
            const picklistData = await this.getPicklistData(fieldName, oapName);
            const picklistItem = picklistData.find(item =>
              item.label === fieldValue || item.value === fieldValue
            );

            if (picklistItem) {
              mappedValue = picklistItem.value;
            }
          }

          // Update the application data with mapped value
          this.setNestedProperty(applicationData, fieldName, mappedValue);

          results.push({
            fieldName,
            originalValue: fieldValue,
            mappedValue,
            status: mappedValue !== fieldValue ? 'success' : 'no_mapping_found',
          });
        } catch (error) {
          results.push({
            fieldName,
            originalValue: fieldValue,
            mappedValue: fieldValue,
            status: 'failed',
            error: error.message,
          });
        }
      }
    }

    return results;
  }

  /**
   * Save transformed application to student application database
   */
  private async saveStudentApplication(
    transformedData: any,
    oapName: string,
    applicationId: string,
    email: string,
  ): Promise<void> {
    try {
      const applicationRecord = {
        PK: email,
        SK: `${oapName}_${applicationId}`,
        applicationId,
        oapName,
        email,
        ...transformedData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await this.dynamoDBService.putObject(
        process.env.STUDENT_DETAILS || 'gus-student-details',
        {
          Item: applicationRecord,
        },
      );

      console.log(`Successfully saved application ${applicationId} for ${email}`);
    } catch (error) {
      console.error(`Failed to save application ${applicationId}:`, error);
      throw error;
    }
  }

  /**
   * Utility method to get nested property value
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Utility method to set nested property value
   */
  private setNestedProperty(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * Transform boolean values based on configuration
   */
  private transformBooleanValue(value: any, rule: any): boolean {
    if (rule.true_values && rule.true_values.includes(value)) {
      return true;
    }
    if (rule.false_values && rule.false_values.includes(value)) {
      return false;
    }
    // Default conversion
    return Boolean(value);
  }

  /**
   * Convert field type based on configuration
   */
  private convertFieldType(value: any, rule: any): any {
    if (!rule.target_type) {
      return value;
    }

    try {
      switch (rule.target_type.toLowerCase()) {
        case 'number':
          if (typeof value === 'string') {
            const numValue = parseFloat(value);
            return isNaN(numValue) ? value : numValue;
          }
          return Number(value);

        case 'integer':
          if (typeof value === 'string') {
            const intValue = parseInt(value, 10);
            return isNaN(intValue) ? value : intValue;
          }
          return parseInt(value, 10);

        case 'string':
          return String(value);

        case 'boolean':
          if (typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            if (['true', '1', 'yes', 'on'].includes(lowerValue)) return true;
            if (['false', '0', 'no', 'off'].includes(lowerValue)) return false;
          }
          return Boolean(value);

        case 'array':
          if (Array.isArray(value)) return value;
          if (typeof value === 'string') {
            // Try to parse as JSON array, or split by delimiter
            try {
              return JSON.parse(value);
            } catch {
              const delimiter = rule.delimiter || ',';
              return value.split(delimiter).map(item => item.trim());
            }
          }
          return [value];

        case 'object':
          if (typeof value === 'object' && value !== null) return value;
          if (typeof value === 'string') {
            try {
              return JSON.parse(value);
            } catch {
              return { value: value };
            }
          }
          return { value: value };

        default:
          console.warn(`Unknown target type: ${rule.target_type}`);
          return value;
      }
    } catch (error) {
      console.warn(`Type conversion failed for value ${value} to ${rule.target_type}:`, error);
      return value; // Return original value if conversion fails
    }
  }

  /**
   * Transform date format utility
   */
  private transformDateFormat(
    dateValue: string,
    _fromFormat: string,
    toFormat: string,
  ): string {
    try {
      // Simple date transformation - can be enhanced with moment.js or date-fns
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) {
        return dateValue; // Return original if invalid date
      }

      if (toFormat === 'ISO') {
        return date.toISOString();
      } else if (toFormat === 'YYYY-MM-DD') {
        return date.toISOString().split('T')[0];
      }

      return dateValue;
    } catch (error) {
      console.warn(`Date transformation failed for ${dateValue}:`, error);
      return dateValue;
    }
  }
}
